#!/usr/bin/env python3
"""
Shiprocket Integration Test Script
=================================

Comprehensive test script to validate Shiprocket integration
with the Allora backend shipping system.

Author: Allora Development Team
Date: 2025-07-14
"""

import os
import sys
import json
from datetime import datetime, timedelta
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Add the order_fulfillment directory to the path
sys.path.append(os.path.join(os.path.dirname(__file__), 'order_fulfillment'))

def print_header(title):
    """Print section header"""
    print(f"\n{'='*60}")
    print(f"{title.center(60)}")
    print(f"{'='*60}")

def test_result(test_name, status, message=""):
    """Print test result"""
    status_symbol = "✅" if status == "PASS" else "❌" if status == "FAIL" else "⚠️"
    print(f"{status_symbol} {test_name:<40} [{status}]")
    if message:
        print(f"   {message}")

def test_environment_variables():
    """Test Shiprocket environment variables"""
    print_header("ENVIRONMENT VARIABLES TEST")
    
    required_vars = {
        'SHIPROCKET_EMAIL': 'Shiprocket account email',
        'SHIPROCKET_PASSWORD': 'Shiprocket account password'
    }
    
    optional_vars = {
        'SHIPROCKET_SANDBOX': 'Sandbox mode flag',
        'SHIPROCKET_DEFAULT_PICKUP_LOCATION': 'Default pickup location',
        'SHIPROCKET_AUTO_AWB': 'Auto AWB generation',
        'SHIPROCKET_AUTO_PICKUP': 'Auto pickup scheduling',
        'SHIPROCKET_ENABLE_COD': 'COD support',
        'SHIPROCKET_ENABLE_INTERNATIONAL': 'International shipping',
        'SHIPROCKET_ENABLE_INSURANCE': 'Insurance support'
    }
    
    all_present = True
    
    # Check required variables
    for var, description in required_vars.items():
        value = os.getenv(var)
        if value:
            masked_value = value[:4] + '*' * (len(value) - 4) if len(value) > 4 else '*' * len(value)
            test_result(var, "PASS", f"Value: {masked_value}")
        else:
            test_result(var, "FAIL", f"Missing: {description}")
            all_present = False
    
    # Check optional variables
    for var, description in optional_vars.items():
        value = os.getenv(var)
        if value:
            test_result(var, "PASS", f"Value: {value}")
        else:
            test_result(var, "INFO", f"Using default: {description}")
    
    return all_present

def test_shiprocket_api_connection():
    """Test Shiprocket API connection and authentication"""
    print_header("SHIPROCKET API CONNECTION TEST")
    
    try:
        from shiprocket_api import ShiprocketAPI
        from carrier_integration import CarrierConfig
        
        # Create Shiprocket API instance
        config = CarrierConfig()
        shiprocket = ShiprocketAPI(config)
        
        test_result("API Instance Creation", "PASS", "ShiprocketAPI instance created")
        
        # Test authentication
        auth_result = shiprocket.authenticate()
        
        if auth_result:
            test_result("API Authentication", "PASS", "Successfully authenticated")
            
            # Test pickup locations
            locations_result = shiprocket.get_pickup_locations()
            
            if locations_result.get('success'):
                pickup_data = locations_result.get('data', {})
                addresses = pickup_data.get('shipping_address', [])
                
                if addresses:
                    test_result("Pickup Locations", "PASS", f"Found {len(addresses)} pickup location(s)")
                    
                    # Show pickup locations
                    for addr in addresses:
                        location_name = addr.get('pickup_location', 'Unknown')
                        city = addr.get('city', 'Unknown')
                        pincode = addr.get('pin_code', 'Unknown')
                        test_result(f"  {location_name}", "INFO", f"{city} - {pincode}")
                else:
                    test_result("Pickup Locations", "WARN", "No pickup locations configured")
            else:
                test_result("Pickup Locations", "FAIL", f"Error: {locations_result.get('error')}")
            
            return True
        else:
            test_result("API Authentication", "FAIL", "Authentication failed - check credentials")
            return False
            
    except ImportError as e:
        test_result("Import Modules", "FAIL", f"Import error: {str(e)}")
        return False
    except Exception as e:
        test_result("API Connection", "FAIL", f"Connection error: {str(e)}")
        return False

def test_rate_calculation():
    """Test Shiprocket rate calculation"""
    print_header("RATE CALCULATION TEST")
    
    try:
        from shiprocket_api import ShiprocketAPI
        from carrier_integration import CarrierConfig
        from order_fulfillment_architecture import Address, Package
        
        # Create API instance
        config = CarrierConfig()
        shiprocket = ShiprocketAPI(config)
        
        # Test addresses
        origin = Address(
            name="Test Store",
            address_line_1="123 Business Street",
            city="Mumbai",
            state="Maharashtra",
            postal_code="400001",
            country="India",
            phone="9876543210"
        )
        
        destination = Address(
            name="Test Customer",
            address_line_1="456 Customer Street",
            city="Delhi",
            state="Delhi",
            postal_code="110001",
            country="India",
            phone="9876543211"
        )
        
        # Test package
        package = Package(
            weight=1.5,
            length=20,
            width=15,
            height=10,
            declared_value=2500,
            description="Test Product"
        )
        
        # Calculate rates
        rates = shiprocket.calculate_rates(origin, destination, [package])
        
        if rates:
            test_result("Rate Calculation", "PASS", f"Found {len(rates)} rate options")
            
            # Show top 3 rates
            for i, rate in enumerate(rates[:3]):
                test_result(f"  Option {i+1}", "INFO", 
                          f"{rate.service_name}: ₹{rate.cost} ({rate.estimated_days} days)")
            
            return True
        else:
            test_result("Rate Calculation", "FAIL", "No rates returned")
            return False
            
    except Exception as e:
        test_result("Rate Calculation", "FAIL", f"Error: {str(e)}")
        return False

def test_carrier_integration():
    """Test Shiprocket integration with carrier framework"""
    print_header("CARRIER INTEGRATION TEST")
    
    try:
        from carrier_integration import CarrierFactory
        from order_fulfillment_architecture import ShippingCarrier
        
        # Create carrier factory
        factory = CarrierFactory()
        
        # Test getting Shiprocket carrier
        shiprocket_carrier = factory.get_carrier(ShippingCarrier.SHIPROCKET)
        test_result("Carrier Factory", "PASS", "Shiprocket carrier instance created")
        
        # Test getting all carriers
        all_carriers = factory.get_all_carriers()
        carrier_names = [carrier.__class__.__name__ for carrier in all_carriers]
        test_result("All Carriers", "PASS", f"Available: {', '.join(carrier_names)}")
        
        return True
        
    except Exception as e:
        test_result("Carrier Integration", "FAIL", f"Error: {str(e)}")
        return False

def test_configuration_validation():
    """Test configuration validation"""
    print_header("CONFIGURATION VALIDATION TEST")
    
    # Test sandbox mode
    sandbox = os.getenv('SHIPROCKET_SANDBOX', 'true').lower() == 'true'
    test_result("Sandbox Mode", "PASS", f"Enabled: {sandbox}")
    
    # Test feature flags
    features = {
        'COD': os.getenv('SHIPROCKET_ENABLE_COD', 'true').lower() == 'true',
        'International': os.getenv('SHIPROCKET_ENABLE_INTERNATIONAL', 'true').lower() == 'true',
        'Insurance': os.getenv('SHIPROCKET_ENABLE_INSURANCE', 'true').lower() == 'true',
        'Auto AWB': os.getenv('SHIPROCKET_AUTO_AWB', 'true').lower() == 'true',
        'Auto Pickup': os.getenv('SHIPROCKET_AUTO_PICKUP', 'true').lower() == 'true'
    }
    
    for feature, enabled in features.items():
        test_result(f"{feature} Support", "PASS" if enabled else "INFO", f"Enabled: {enabled}")
    
    # Test default dimensions
    dimensions = {
        'Length': os.getenv('SHIPROCKET_DEFAULT_LENGTH', '10'),
        'Width': os.getenv('SHIPROCKET_DEFAULT_WIDTH', '10'),
        'Height': os.getenv('SHIPROCKET_DEFAULT_HEIGHT', '10'),
        'Weight': os.getenv('SHIPROCKET_DEFAULT_WEIGHT', '0.5')
    }
    
    dims_str = f"{dimensions['Length']}x{dimensions['Width']}x{dimensions['Height']} cm, {dimensions['Weight']} kg"
    test_result("Default Package Size", "PASS", dims_str)
    
    return True

def generate_test_report():
    """Generate test report"""
    print_header("TEST REPORT GENERATION")
    
    report = {
        "timestamp": datetime.now().isoformat(),
        "test_results": {
            "environment_variables": test_environment_variables(),
            "api_connection": False,
            "rate_calculation": False,
            "carrier_integration": False,
            "configuration": test_configuration_validation()
        },
        "shiprocket_config": {
            "email": os.getenv('SHIPROCKET_EMAIL', 'Not configured'),
            "sandbox": os.getenv('SHIPROCKET_SANDBOX', 'true'),
            "pickup_location": os.getenv('SHIPROCKET_DEFAULT_PICKUP_LOCATION', 'primary'),
            "features": {
                "cod": os.getenv('SHIPROCKET_ENABLE_COD', 'true'),
                "international": os.getenv('SHIPROCKET_ENABLE_INTERNATIONAL', 'true'),
                "insurance": os.getenv('SHIPROCKET_ENABLE_INSURANCE', 'true')
            }
        }
    }
    
    # Run API tests if environment is configured
    if report["test_results"]["environment_variables"]:
        report["test_results"]["api_connection"] = test_shiprocket_api_connection()
        
        if report["test_results"]["api_connection"]:
            report["test_results"]["rate_calculation"] = test_rate_calculation()
        
        report["test_results"]["carrier_integration"] = test_carrier_integration()
    
    # Save report
    with open('shiprocket_test_report.json', 'w') as f:
        json.dump(report, f, indent=2)
    
    test_result("Test Report", "PASS", "Report saved to shiprocket_test_report.json")
    
    return report

def show_integration_status(report):
    """Show integration status summary"""
    print_header("INTEGRATION STATUS SUMMARY")
    
    results = report["test_results"]
    passed_tests = sum(results.values())
    total_tests = len(results)
    
    print(f"Tests Passed: {passed_tests}/{total_tests}")
    print(f"Success Rate: {(passed_tests/total_tests)*100:.1f}%")
    
    if passed_tests == total_tests:
        print("\n✅ Shiprocket integration is fully configured and working!")
        print("🎉 Your Allora backend is ready for multi-carrier shipping.")
        
        print("\n📋 Available APIs:")
        print("   • POST /api/fulfillment/rates - Calculate shipping rates")
        print("   • POST /api/fulfillment/shipments - Create shipments")
        print("   • GET /api/fulfillment/shipments/{id}/track - Track shipments")
        print("   • POST /api/fulfillment/pickups - Schedule pickups")
        
    elif results["environment_variables"] and results["configuration"]:
        print("\n⚠️  Shiprocket is configured but some tests failed.")
        print("This might be due to network issues or API limitations.")
        print("The integration should still work for basic functionality.")
        
    else:
        print("\n❌ Shiprocket integration needs attention.")
        print("Please check the failed tests and fix the configuration.")

def main():
    """Main test function"""
    print("🚚 Shiprocket Integration Test for Allora Backend")
    print("=" * 60)
    
    # Generate comprehensive test report
    report = generate_test_report()
    
    # Show integration status
    show_integration_status(report)
    
    # Return exit code based on results
    results = report["test_results"]
    critical_tests = ["environment_variables", "configuration"]
    critical_passed = all(results[test] for test in critical_tests)
    
    return 0 if critical_passed else 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
