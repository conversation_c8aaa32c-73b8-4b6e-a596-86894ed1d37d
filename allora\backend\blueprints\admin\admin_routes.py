"""
Admin Routes
============

Admin management endpoints with consistent response format and proper authorization.
"""

from flask import request
from datetime import datetime, timedelta
import logging

from ..common.versioning import create_versioned_blueprint
from ..common.response_wrapper import (
    success_response, error_response, paginated_response, 
    not_found_response, validation_error_response
)
from ..common.auth_decorators import admin_required_v2, rate_limit_v2, validate_content_type
from ..common.validators import (
    validate_json, validate_required_fields, validate_pagination,
    validate_email
)

logger = logging.getLogger(__name__)

# Create versioned blueprint
admin_bp = create_versioned_blueprint('admin', __name__, url_prefix='/admin')

@admin_bp.route('/dashboard', methods=['GET'])
@admin_required_v2()
def get_dashboard_overview(user, admin_user):
    """
    Get admin dashboard overview with key metrics.
    
    GET /api/v1/admin/dashboard
    """
    try:
        from app import db, User, Product, Order, CommunityPost, Sales
        
        # Calculate date ranges
        today = datetime.utcnow().date()
        yesterday = today - timedelta(days=1)
        last_30_days = today - timedelta(days=30)
        
        # User metrics
        total_users = User.query.filter_by(is_active=True).count()
        new_users_today = User.query.filter(
            db.func.date(User.created_at) == today
        ).count()
        new_users_yesterday = User.query.filter(
            db.func.date(User.created_at) == yesterday
        ).count()
        
        # Product metrics
        total_products = Product.query.filter_by(is_active=True).count()
        low_stock_products = Product.query.filter(
            Product.stock_quantity < 10,
            Product.stock_quantity > 0,
            Product.is_active == True
        ).count()
        out_of_stock_products = Product.query.filter(
            Product.stock_quantity == 0,
            Product.is_active == True
        ).count()
        
        # Order metrics
        total_orders = Order.query.count()
        pending_orders = Order.query.filter_by(status='pending').count()
        orders_today = Order.query.filter(
            db.func.date(Order.created_at) == today
        ).count()
        
        # Sales metrics
        total_revenue = db.session.query(
            db.func.sum(Sales.total_amount)
        ).scalar() or 0
        
        revenue_last_30_days = db.session.query(
            db.func.sum(Sales.total_amount)
        ).filter(
            Sales.created_at >= last_30_days
        ).scalar() or 0
        
        # Community metrics
        total_posts = CommunityPost.query.filter_by(is_active=True).count()
        posts_today = CommunityPost.query.filter(
            db.func.date(CommunityPost.created_at) == today,
            CommunityPost.is_active == True
        ).count()
        
        # Recent activity
        recent_orders = Order.query.order_by(
            Order.created_at.desc()
        ).limit(5).all()
        
        recent_users = User.query.filter_by(is_active=True).order_by(
            User.created_at.desc()
        ).limit(5).all()
        
        dashboard_data = {
            "metrics": {
                "users": {
                    "total": total_users,
                    "new_today": new_users_today,
                    "growth": new_users_today - new_users_yesterday
                },
                "products": {
                    "total": total_products,
                    "low_stock": low_stock_products,
                    "out_of_stock": out_of_stock_products
                },
                "orders": {
                    "total": total_orders,
                    "pending": pending_orders,
                    "today": orders_today
                },
                "revenue": {
                    "total": float(total_revenue),
                    "last_30_days": float(revenue_last_30_days)
                },
                "community": {
                    "total_posts": total_posts,
                    "posts_today": posts_today
                }
            },
            "recent_activity": {
                "orders": [
                    {
                        "id": order.id,
                        "user_id": order.user_id,
                        "status": order.status,
                        "total": float(order.total_amount),
                        "created_at": order.created_at.isoformat() if order.created_at else None
                    }
                    for order in recent_orders
                ],
                "users": [
                    {
                        "id": user.id,
                        "name": f"{user.first_name} {user.last_name}",
                        "email": user.email,
                        "created_at": user.created_at.isoformat() if user.created_at else None
                    }
                    for user in recent_users
                ]
            }
        }
        
        return success_response(
            data=dashboard_data,
            message="Dashboard overview retrieved successfully"
        )
        
    except Exception as e:
        logger.error(f"Get dashboard overview error: {e}")
        return error_response(
            message="Failed to retrieve dashboard overview",
            status_code=500,
            error_code="DASHBOARD_OVERVIEW_FAILED"
        )

@admin_bp.route('/users', methods=['GET'])
@admin_required_v2('user_management')
def get_users(user, admin_user):
    """
    Get users with pagination and filtering for admin management.
    
    GET /api/v1/admin/users
    """
    try:
        page = request.args.get('page', 1, type=int)
        per_page = request.args.get('per_page', 20, type=int)
        search = request.args.get('search', '').strip()
        status = request.args.get('status')  # active, inactive, suspended
        user_type = request.args.get('user_type')  # customer, seller, admin
        date_from = request.args.get('date_from')
        date_to = request.args.get('date_to')

        # Validate pagination
        page, per_page = validate_pagination(page, per_page, max_per_page=100)

        from app import db, User, Order, Seller

        # Build query
        query = User.query

        # Apply filters
        if search:
            search_filter = f'%{search}%'
            query = query.filter(
                db.or_(
                    User.first_name.ilike(search_filter),
                    User.last_name.ilike(search_filter),
                    User.email.ilike(search_filter)
                )
            )

        if status:
            if status == 'active':
                query = query.filter_by(is_active=True)
            elif status == 'inactive':
                query = query.filter_by(is_active=False)
            elif status == 'suspended':
                query = query.filter_by(is_suspended=True)

        if user_type:
            query = query.filter_by(user_type=user_type)

        if date_from:
            try:
                from_date = datetime.strptime(date_from, '%Y-%m-%d')
                query = query.filter(User.created_at >= from_date)
            except ValueError:
                return validation_error_response(
                    errors={"date_from": ["Invalid date format. Use YYYY-MM-DD"]},
                    message="Invalid date format"
                )

        if date_to:
            try:
                to_date = datetime.strptime(date_to, '%Y-%m-%d')
                query = query.filter(User.created_at <= to_date)
            except ValueError:
                return validation_error_response(
                    errors={"date_to": ["Invalid date format. Use YYYY-MM-DD"]},
                    message="Invalid date format"
                )
        
        query = query.order_by(User.created_at.desc())
        
        # Execute paginated query
        paginated_users = query.paginate(
            page=page,
            per_page=per_page,
            error_out=False
        )
        
        users_data = []
        for user_item in paginated_users.items:
            # Get user statistics
            total_orders = Order.query.filter_by(user_id=user_item.id).count()
            total_spent = db.session.query(
                db.func.sum(Order.total_amount)
            ).filter(
                Order.user_id == user_item.id,
                Order.status.in_(['completed', 'delivered'])
            ).scalar() or 0.0

            # Check if user is a seller
            seller_info = None
            if getattr(user_item, 'user_type', 'customer') == 'seller':
                seller = Seller.query.filter_by(user_id=user_item.id).first()
                if seller:
                    seller_info = {
                        "seller_id": seller.id,
                        "business_name": seller.business_name,
                        "status": seller.status,
                        "is_verified": seller.is_verified
                    }

            users_data.append({
                "id": user_item.id,
                "email": user_item.email,
                "first_name": user_item.first_name,
                "last_name": user_item.last_name,
                "phone": getattr(user_item, 'phone', None),
                "user_type": getattr(user_item, 'user_type', 'customer'),
                "is_active": user_item.is_active,
                "is_suspended": getattr(user_item, 'is_suspended', False),
                "email_verified": getattr(user_item, 'email_verified', False),
                "phone_verified": getattr(user_item, 'phone_verified', False),
                "seller_info": seller_info,
                "statistics": {
                    "total_orders": total_orders,
                    "total_spent": float(total_spent),
                    "last_login": getattr(user_item, 'last_login', None)
                },
                "created_at": user_item.created_at.isoformat() if user_item.created_at else None,
                "updated_at": user_item.updated_at.isoformat() if user_item.updated_at else None
            })
        
        return paginated_response(
            data=users_data,
            page=page,
            per_page=per_page,
            total=paginated_users.total,
            message="Users retrieved successfully",
            meta={
                "filters_applied": {
                    "status": status,
                    "user_type": user_type,
                    "search": search,
                    "date_from": date_from,
                    "date_to": date_to
                }
            }
        )
        
    except Exception as e:
        logger.error(f"Get users error: {e}")
        return error_response(
            message="Failed to retrieve users",
            status_code=500,
            error_code="USERS_FETCH_FAILED"
        )

@admin_bp.route('/users/<int:user_id>/status', methods=['PUT'])
@admin_required_v2('user_management')
@validate_content_type()
def update_user_status(user, admin_user, user_id):
    """
    Update user account status (activate/deactivate).
    
    PUT /api/v1/admin/users/{user_id}/status
    """
    try:
        # Validate JSON
        is_valid, data = validate_json()
        if not is_valid:
            return data
        
        # Validate required fields
        required_fields = ['is_active']
        is_valid, error_resp = validate_required_fields(data, required_fields)
        if not is_valid:
            return error_resp
        
        is_active = data['is_active']
        reason = data.get('reason', '')
        
        if not isinstance(is_active, bool):
            return validation_error_response(
                errors={"is_active": ["Must be a boolean value"]},
                message="Invalid status value"
            )
        
        from app import db, User
        
        # Find user
        target_user = User.query.get(user_id)
        if not target_user:
            return not_found_response("User", user_id)
        
        # Prevent admin from deactivating themselves
        if target_user.id == user.id:
            return error_response(
                message="Cannot modify your own account status",
                status_code=400,
                error_code="SELF_MODIFICATION_NOT_ALLOWED"
            )
        
        # Update user status
        old_status = target_user.is_active
        target_user.is_active = is_active
        target_user.updated_at = datetime.utcnow()
        
        # Log the action (you might want to create an AdminActionLog model)
        action_type = "activate" if is_active else "deactivate"
        logger.info(f"Admin {user.id} {action_type}d user {user_id}. Reason: {reason}")
        
        db.session.commit()
        
        user_data = {
            "id": target_user.id,
            "name": f"{target_user.first_name} {target_user.last_name}",
            "email": target_user.email,
            "is_active": target_user.is_active,
            "status_changed": old_status != is_active,
            "updated_at": target_user.updated_at.isoformat()
        }
        
        return success_response(
            data=user_data,
            message=f"User account {'activated' if is_active else 'deactivated'} successfully"
        )
        
    except Exception as e:
        logger.error(f"Update user status error: {e}")
        db.session.rollback()
        return error_response(
            message="Failed to update user status",
            status_code=500,
            error_code="USER_STATUS_UPDATE_FAILED"
        )

@admin_bp.route('/products/pending', methods=['GET'])
@admin_required_v2('content_management')
def get_pending_products(user, admin_user):
    """
    Get products pending admin approval.
    
    GET /api/v1/admin/products/pending
    """
    try:
        page = request.args.get('page', 1, type=int)
        per_page = request.args.get('per_page', 20, type=int)
        
        # Validate pagination
        page, per_page = validate_pagination(page, per_page, max_per_page=50)
        
        from app import Product, User
        
        # Get pending products (assuming there's a status field)
        query = Product.query.filter_by(
            status='pending'  # Assuming products have a status field
        ).order_by(Product.created_at.desc())
        
        # Execute paginated query
        paginated_products = query.paginate(
            page=page,
            per_page=per_page,
            error_out=False
        )
        
        products_data = []
        for product in paginated_products.items:
            # Get seller info if available
            seller_info = None
            if hasattr(product, 'seller_id') and product.seller_id:
                from app import Seller
                seller = Seller.query.get(product.seller_id)
                if seller:
                    seller_user = User.query.get(seller.user_id)
                    seller_info = {
                        "id": seller.id,
                        "business_name": seller.business_name,
                        "contact_email": seller_user.email if seller_user else None
                    }
            
            products_data.append({
                "id": product.id,
                "name": product.name,
                "description": product.description,
                "price": float(product.price),
                "category": product.category,
                "stock_quantity": product.stock_quantity,
                "seller": seller_info,
                "created_at": product.created_at.isoformat() if product.created_at else None
            })
        
        return paginated_response(
            data=products_data,
            page=page,
            per_page=per_page,
            total=paginated_products.total,
            message="Pending products retrieved successfully"
        )
        
    except Exception as e:
        logger.error(f"Get pending products error: {e}")
        return error_response(
            message="Failed to retrieve pending products",
            status_code=500,
            error_code="PENDING_PRODUCTS_FETCH_FAILED"
        )

@admin_bp.route('/system/health', methods=['GET'])
@admin_required_v2()
def get_system_health(user, admin_user):
    """
    Get system health status and metrics.
    
    GET /api/v1/admin/system/health
    """
    try:
        from app import db
        import psutil
        import os
        
        # Database health
        try:
            db.session.execute('SELECT 1')
            db_status = "healthy"
            db_error = None
        except Exception as e:
            db_status = "unhealthy"
            db_error = str(e)
        
        # System metrics
        cpu_percent = psutil.cpu_percent(interval=1)
        memory = psutil.virtual_memory()
        disk = psutil.disk_usage('/')
        
        # Application metrics
        uptime = datetime.utcnow() - datetime.fromtimestamp(psutil.Process(os.getpid()).create_time())
        
        health_data = {
            "status": "healthy" if db_status == "healthy" else "degraded",
            "timestamp": datetime.utcnow().isoformat(),
            "services": {
                "database": {
                    "status": db_status,
                    "error": db_error
                },
                "application": {
                    "status": "healthy",
                    "uptime_seconds": int(uptime.total_seconds())
                }
            },
            "system_metrics": {
                "cpu_percent": cpu_percent,
                "memory": {
                    "total": memory.total,
                    "available": memory.available,
                    "percent": memory.percent
                },
                "disk": {
                    "total": disk.total,
                    "free": disk.free,
                    "percent": (disk.used / disk.total) * 100
                }
            }
        }
        
        return success_response(
            data=health_data,
            message="System health retrieved successfully"
        )
        
    except Exception as e:
        logger.error(f"Get system health error: {e}")
        return error_response(
            message="Failed to retrieve system health",
            status_code=500,
            error_code="SYSTEM_HEALTH_FAILED"
        )

@admin_bp.route('/sign-in', methods=['POST'])
@rate_limit_v2(limit=3, window=300, per='ip', block_duration=1800)
@validate_content_type()
def admin_sign_in():
    """
    Admin login endpoint.

    POST /api/v1/admin/sign-in
    """
    try:
        # Validate JSON
        is_valid, data = validate_json()
        if not is_valid:
            return data

        # Validate required fields
        required_fields = ['email', 'password']
        is_valid, error_resp = validate_required_fields(data, required_fields)
        if not is_valid:
            return error_resp

        email = data['email'].strip().lower()
        password = data['password']

        from app import db, User, AdminUser, bcrypt
        from flask_jwt_extended import create_access_token, create_refresh_token

        # Find user
        user = User.query.filter_by(email=email).first()

        if not user or not bcrypt.check_password_hash(user.password_hash, password):
            return unauthorized_response("Invalid email or password")

        if not user.is_active:
            return forbidden_response("Account is deactivated")

        # Check if user is an admin
        admin_user = AdminUser.query.filter_by(user_id=user.id).first()
        if not admin_user:
            return forbidden_response("Admin access required")

        # Create tokens with admin role
        additional_claims = {"role": "admin", "admin_id": admin_user.id}
        access_token = create_access_token(
            identity=user.id,
            additional_claims=additional_claims
        )
        refresh_token = create_refresh_token(identity=user.id)

        # Update last login
        user.last_login = datetime.utcnow()
        db.session.commit()

        user_data = {
            "id": user.id,
            "email": user.email,
            "first_name": user.first_name,
            "last_name": user.last_name,
            "role": "admin",
            "admin_permissions": {
                "user_management": getattr(admin_user, 'user_management', False),
                "content_management": getattr(admin_user, 'content_management', False),
                "system_management": getattr(admin_user, 'system_management', False),
                "analytics_access": getattr(admin_user, 'analytics_access', False)
            }
        }

        return success_response(
            data={
                "user": user_data,
                "access_token": access_token,
                "refresh_token": refresh_token
            },
            message="Admin login successful"
        )

    except Exception as e:
        logger.error(f"Admin login error: {e}")
        return error_response(
            message="Admin login failed",
            status_code=500,
            error_code="ADMIN_LOGIN_FAILED"
        )

# Admin User Management Endpoints
                query = query.filter(User.created_at <= to_date)
            except ValueError:
                return validation_error_response(
                    errors={"date_to": ["Invalid date format. Use YYYY-MM-DD"]},
                    message="Invalid date format"
                )

        query = query.order_by(User.created_at.desc())

        # Execute paginated query
        paginated_users = query.paginate(
            page=page,
            per_page=per_page,
            error_out=False
        )

        # Format users data
        users_data = []
        for user_obj in paginated_users.items:
            # Get user statistics
            total_orders = Order.query.filter_by(user_id=user_obj.id).count()
            total_spent = db.session.query(
                db.func.sum(Order.total_amount)
            ).filter(
                Order.user_id == user_obj.id,
                Order.status.in_(['completed', 'delivered'])
            ).scalar() or 0.0

            # Check if user is a seller
            seller_info = None
            if user_obj.user_type == 'seller':
                seller = Seller.query.filter_by(user_id=user_obj.id).first()
                if seller:
                    seller_info = {
                        "seller_id": seller.id,
                        "business_name": seller.business_name,
                        "status": seller.status,
                        "is_verified": seller.is_verified
                    }

            users_data.append({
                "id": user_obj.id,
                "email": user_obj.email,
                "first_name": user_obj.first_name,
                "last_name": user_obj.last_name,
                "user_type": getattr(user_obj, 'user_type', 'customer'),
                "is_active": user_obj.is_active,
                "is_suspended": getattr(user_obj, 'is_suspended', False),
                "email_verified": getattr(user_obj, 'email_verified', False),
                "phone_verified": getattr(user_obj, 'phone_verified', False),
                "seller_info": seller_info,
                "statistics": {
                    "total_orders": total_orders,
                    "total_spent": float(total_spent),
                    "last_login": getattr(user_obj, 'last_login', None)
                },
                "created_at": user_obj.created_at.isoformat() if user_obj.created_at else None,
                "updated_at": user_obj.updated_at.isoformat() if user_obj.updated_at else None
            })

        return paginated_response(
            data=users_data,
            page=page,
            per_page=per_page,
            total=paginated_users.total,
            message="Users retrieved successfully",
            meta={
                "filters_applied": {
                    "status": status,
                    "user_type": user_type,
                    "search": search,
                    "date_from": date_from,
                    "date_to": date_to
                }
            }
        )

    except Exception as e:
        logger.error(f"Get users error: {e}")
        return error_response(
            message="Failed to retrieve users",
            status_code=500,
            error_code="ADMIN_USERS_FETCH_FAILED"
        )

# Admin Seller Management Endpoints

@admin_bp.route('/sellers', methods=['GET'])
@admin_required_v2()
@rate_limit_v2(limit=60, window=60, per='user')
def get_sellers(user, admin_user):
    """
    Get all sellers with pagination and filtering.

    GET /api/v1/admin/sellers
    """
    try:
        page = request.args.get('page', 1, type=int)
        per_page = request.args.get('per_page', 20, type=int)
        status = request.args.get('status')  # pending, approved, rejected, suspended
        verification_status = request.args.get('verification_status')  # verified, unverified
        search = request.args.get('search', '').strip()

        # Validate pagination
        page, per_page = validate_pagination(page, per_page, max_per_page=100)

        from app import db, Seller, User, Product, Order, SellerStore

        # Build query
        query = Seller.query

        # Apply filters
        if status:
            query = query.filter_by(status=status)

        if verification_status:
            if verification_status == 'verified':
                query = query.filter_by(is_verified=True)
            elif verification_status == 'unverified':
                query = query.filter_by(is_verified=False)

        if search:
            query = query.filter(
                db.or_(
                    Seller.business_name.ilike(f'%{search}%'),
                    Seller.business_email.ilike(f'%{search}%'),
                    Seller.contact_person_name.ilike(f'%{search}%')
                )
            )

        query = query.order_by(Seller.created_at.desc())

        # Execute paginated query
        paginated_sellers = query.paginate(
            page=page,
            per_page=per_page,
            error_out=False
        )

        # Format sellers data
        sellers_data = []
        for seller in paginated_sellers.items:
            # Get seller statistics
            total_products = Product.query.filter_by(seller_id=seller.id, is_active=True).count()
            total_orders = db.session.query(Order).join(
                Product, Order.id == Product.id  # This would need proper OrderItem join
            ).filter(Product.seller_id == seller.id).count()

            # Get user info
            user_info = None
            if seller.user_id:
                seller_user = User.query.get(seller.user_id)
                if seller_user:
                    user_info = {
                        "user_id": seller_user.id,
                        "email": seller_user.email,
                        "is_active": seller_user.is_active,
                        "last_login": getattr(seller_user, 'last_login', None)
                    }

            # Get store info
            store = SellerStore.query.filter_by(seller_id=seller.id).first()
            store_info = None
            if store:
                store_info = {
                    "store_name": store.store_name,
                    "store_slug": store.store_slug,
                    "is_active": store.is_active
                }

            sellers_data.append({
                "id": seller.id,
                "business_name": seller.business_name,
                "business_email": seller.business_email,
                "business_phone": seller.business_phone,
                "business_type": seller.business_type,
                "contact_person_name": seller.contact_person_name,
                "status": seller.status,
                "is_verified": seller.is_verified,
                "commission_rate": getattr(seller, 'commission_rate', 10.0),
                "user_info": user_info,
                "store_info": store_info,
                "statistics": {
                    "total_products": total_products,
                    "total_orders": total_orders,
                    "rating": getattr(seller, 'rating', 0.0),
                    "total_reviews": getattr(seller, 'total_reviews', 0)
                },
                "created_at": seller.created_at.isoformat() if seller.created_at else None,
                "updated_at": seller.updated_at.isoformat() if seller.updated_at else None
            })

        return paginated_response(
            data=sellers_data,
            page=page,
            per_page=per_page,
            total=paginated_sellers.total,
            message="Sellers retrieved successfully",
            meta={
                "filters_applied": {
                    "status": status,
                    "verification_status": verification_status,
                    "search": search
                }
            }
        )

    except Exception as e:
        logger.error(f"Get sellers error: {e}")
        return error_response(
            message="Failed to retrieve sellers",
            status_code=500,
            error_code="ADMIN_SELLERS_FETCH_FAILED"
        )

@admin_bp.route('/sellers/<int:seller_id>', methods=['GET'])
@admin_required_v2()
@rate_limit_v2(limit=60, window=60, per='user')
def get_seller_details(user, admin_user, seller_id):
    """
    Get detailed seller information.

    GET /api/v1/admin/sellers/{seller_id}
    """
    try:
        from app import db, Seller, User, Product, Order, SellerStore, AdminActionLog

        # Get seller
        seller = Seller.query.get(seller_id)
        if not seller:
            return not_found_response("Seller", seller_id)

        # Get user info
        user_info = None
        if seller.user_id:
            seller_user = User.query.get(seller.user_id)
            if seller_user:
                user_info = {
                    "user_id": seller_user.id,
                    "email": seller_user.email,
                    "first_name": seller_user.first_name,
                    "last_name": seller_user.last_name,
                    "is_active": seller_user.is_active,
                    "email_verified": getattr(seller_user, 'email_verified', False),
                    "created_at": seller_user.created_at.isoformat() if seller_user.created_at else None,
                    "last_login": getattr(seller_user, 'last_login', None)
                }

        # Get store info
        store = SellerStore.query.filter_by(seller_id=seller.id).first()
        store_info = None
        if store:
            store_info = {
                "id": store.id,
                "store_name": store.store_name,
                "store_slug": store.store_slug,
                "store_description": store.store_description,
                "store_logo": store.store_logo,
                "store_banner": store.store_banner,
                "is_active": store.is_active,
                "created_at": store.created_at.isoformat() if store.created_at else None
            }

        # Get detailed statistics
        total_products = Product.query.filter_by(seller_id=seller.id).count()
        active_products = Product.query.filter_by(seller_id=seller.id, is_active=True).count()

        # Get recent admin actions on this seller
        recent_actions = AdminActionLog.query.filter_by(
            target_type='seller',
            target_id=seller_id
        ).order_by(AdminActionLog.created_at.desc()).limit(10).all()

        actions_data = []
        for action in recent_actions:
            admin_user_obj = User.query.get(action.admin_user_id)
            actions_data.append({
                "id": action.id,
                "action_type": action.action_type,
                "admin_user": f"{admin_user_obj.first_name} {admin_user_obj.last_name}" if admin_user_obj else "Unknown",
                "details": action.details or {},
                "created_at": action.created_at.isoformat() if action.created_at else None
            })

        seller_data = {
            "id": seller.id,
            "business_name": seller.business_name,
            "business_email": seller.business_email,
            "business_phone": seller.business_phone,
            "business_address": seller.business_address,
            "business_type": seller.business_type,
            "contact_person_name": seller.contact_person_name,
            "business_description": seller.business_description,
            "website_url": seller.website_url,
            "gst_number": seller.gst_number,
            "status": seller.status,
            "is_verified": seller.is_verified,
            "commission_rate": getattr(seller, 'commission_rate', 10.0),
            "user_info": user_info,
            "store_info": store_info,
            "statistics": {
                "total_products": total_products,
                "active_products": active_products,
                "rating": getattr(seller, 'rating', 0.0),
                "total_reviews": getattr(seller, 'total_reviews', 0),
                "total_sales": getattr(seller, 'total_sales', 0)
            },
            "recent_actions": actions_data,
            "created_at": seller.created_at.isoformat() if seller.created_at else None,
            "updated_at": seller.updated_at.isoformat() if seller.updated_at else None
        }

        return success_response(
            data=seller_data,
            message="Seller details retrieved successfully"
        )

    except Exception as e:
        logger.error(f"Get seller details error: {e}")
        return error_response(
            message="Failed to retrieve seller details",
            status_code=500,
            error_code="ADMIN_SELLER_DETAILS_FETCH_FAILED"
        )

@admin_bp.route('/sellers/<int:seller_id>/status', methods=['PUT'])
@admin_required_v2()
@validate_content_type()
@rate_limit_v2(limit=30, window=300, per='user')
def update_seller_status(user, admin_user, seller_id):
    """
    Update seller status (approve, reject, suspend).

    PUT /api/v1/admin/sellers/{seller_id}/status
    """
    try:
        # Validate JSON
        is_valid, data = validate_json()
        if not is_valid:
            return data

        # Validate required fields
        required_fields = ['status']
        is_valid, error_resp = validate_required_fields(data, required_fields)
        if not is_valid:
            return error_resp

        new_status = data['status']
        reason = data.get('reason', '').strip()

        # Validate status
        valid_statuses = ['pending', 'approved', 'rejected', 'suspended']
        if new_status not in valid_statuses:
            return validation_error_response(
                errors={"status": [f"Must be one of: {', '.join(valid_statuses)}"]},
                message="Invalid status"
            )

        from app import db, Seller, User
        import jwt
        from flask import current_app

        # Get seller
        seller = Seller.query.get(seller_id)
        if not seller:
            return not_found_response("Seller", seller_id)

        old_status = seller.status
        seller.status = new_status
        seller.updated_at = datetime.utcnow()

        # Handle approval workflow
        if new_status == 'approved' and old_status != 'approved':
            # Generate password setup token for new sellers
            if not seller.user_id:
                token_payload = {
                    'seller_id': seller.id,
                    'exp': datetime.utcnow() + timedelta(days=7)  # 7 days to setup password
                }
                setup_token = jwt.encode(token_payload, current_app.config['SECRET_KEY'], algorithm='HS256')

                # TODO: Send email with setup token
                # send_seller_approval_email(seller, setup_token)

        # Log admin action
        from app import AdminActionLog
        action_log = AdminActionLog(
            admin_user_id=user.id,
            action_type='seller_status_update',
            target_type='seller',
            target_id=seller_id,
            details={
                'old_status': old_status,
                'new_status': new_status,
                'reason': reason
            },
            created_at=datetime.utcnow()
        )

        db.session.add(action_log)
        db.session.commit()

        return success_response(
            data={
                "seller_id": seller_id,
                "old_status": old_status,
                "new_status": new_status,
                "updated_at": seller.updated_at.isoformat()
            },
            message=f"Seller status updated to {new_status}"
        )

    except Exception as e:
        logger.error(f"Update seller status error: {e}")
        db.session.rollback()
        return error_response(
            message="Failed to update seller status",
            status_code=500,
            error_code="ADMIN_SELLER_STATUS_UPDATE_FAILED"
        )

@admin_bp.route('/sellers/<int:seller_id>/commission', methods=['PUT'])
@admin_required_v2()
@validate_content_type()
@rate_limit_v2(limit=30, window=300, per='user')
def update_seller_commission(user, admin_user, seller_id):
    """
    Update seller commission rate.

    PUT /api/v1/admin/sellers/{seller_id}/commission
    """
    try:
        # Validate JSON
        is_valid, data = validate_json()
        if not is_valid:
            return data

        # Validate required fields
        required_fields = ['commission_rate']
        is_valid, error_resp = validate_required_fields(data, required_fields)
        if not is_valid:
            return error_resp

        commission_rate = data['commission_rate']
        reason = data.get('reason', '').strip()

        # Validate commission rate
        if not isinstance(commission_rate, (int, float)) or commission_rate < 0 or commission_rate > 50:
            return validation_error_response(
                errors={"commission_rate": ["Commission rate must be between 0 and 50"]},
                message="Invalid commission rate"
            )

        from app import db, Seller

        # Get seller
        seller = Seller.query.get(seller_id)
        if not seller:
            return not_found_response("Seller", seller_id)

        old_commission_rate = getattr(seller, 'commission_rate', 10.0)
        seller.commission_rate = commission_rate
        seller.updated_at = datetime.utcnow()

        # Log admin action
        from app import AdminActionLog
        action_log = AdminActionLog(
            admin_user_id=user.id,
            action_type='seller_commission_update',
            target_type='seller',
            target_id=seller_id,
            details={
                'old_commission_rate': old_commission_rate,
                'new_commission_rate': commission_rate,
                'reason': reason
            },
            created_at=datetime.utcnow()
        )

        db.session.add(action_log)
        db.session.commit()

        return success_response(
            data={
                "seller_id": seller_id,
                "old_commission_rate": old_commission_rate,
                "new_commission_rate": commission_rate,
                "updated_at": seller.updated_at.isoformat()
            },
            message="Seller commission rate updated successfully"
        )

    except Exception as e:
        logger.error(f"Update seller commission error: {e}")
        db.session.rollback()
        return error_response(
            message="Failed to update seller commission",
            status_code=500,
            error_code="ADMIN_SELLER_COMMISSION_UPDATE_FAILED"
        )

# Admin Product Management Endpoints

@admin_bp.route('/products', methods=['GET'])
@admin_required_v2()
@rate_limit_v2(limit=60, window=60, per='user')
def get_products(user, admin_user):
    """
    Get all products with pagination and filtering.

    GET /api/v1/admin/products
    """
    try:
        page = request.args.get('page', 1, type=int)
        per_page = request.args.get('per_page', 20, type=int)
        status = request.args.get('status')  # active, inactive, pending
        category = request.args.get('category')
        seller_id = request.args.get('seller_id', type=int)
        search = request.args.get('search', '').strip()

        # Validate pagination
        page, per_page = validate_pagination(page, per_page, max_per_page=100)

        from app import db, Product, Seller, ProductImage

        # Build query
        query = Product.query

        # Apply filters
        if status:
            if status == 'active':
                query = query.filter_by(is_active=True)
            elif status == 'inactive':
                query = query.filter_by(is_active=False)
            elif status == 'pending':
                query = query.filter_by(status='pending')

        if category:
            query = query.filter(Product.category.ilike(f'%{category}%'))

        if seller_id:
            query = query.filter_by(seller_id=seller_id)

        if search:
            query = query.filter(
                db.or_(
                    Product.name.ilike(f'%{search}%'),
                    Product.description.ilike(f'%{search}%'),
                    Product.sku.ilike(f'%{search}%')
                )
            )

        query = query.order_by(Product.created_at.desc())

        # Execute paginated query
        paginated_products = query.paginate(
            page=page,
            per_page=per_page,
            error_out=False
        )

        # Format products data
        products_data = []
        for product in paginated_products.items:
            # Get seller info
            seller = Seller.query.get(product.seller_id) if product.seller_id else None

            # Get first product image
            first_image = ProductImage.query.filter_by(product_id=product.id).first()

            products_data.append({
                "id": product.id,
                "name": product.name,
                "sku": product.sku,
                "price": float(product.price),
                "category": product.category,
                "brand": getattr(product, 'brand', None),
                "stock_quantity": product.stock_quantity,
                "is_active": product.is_active,
                "status": getattr(product, 'status', 'active'),
                "image_url": first_image.image_url if first_image else None,
                "seller": {
                    "id": seller.id if seller else None,
                    "business_name": seller.business_name if seller else "Allora Direct",
                    "status": seller.status if seller else "approved"
                },
                "statistics": {
                    "total_sales": getattr(product, 'total_sales', 0),
                    "average_rating": product.average_rating or 0.0,
                    "total_reviews": product.total_reviews or 0
                },
                "created_at": product.created_at.isoformat() if product.created_at else None,
                "updated_at": product.updated_at.isoformat() if product.updated_at else None
            })

        return paginated_response(
            data=products_data,
            page=page,
            per_page=per_page,
            total=paginated_products.total,
            message="Products retrieved successfully",
            meta={
                "filters_applied": {
                    "status": status,
                    "category": category,
                    "seller_id": seller_id,
                    "search": search
                }
            }
        )

    except Exception as e:
        logger.error(f"Get products error: {e}")
        return error_response(
            message="Failed to retrieve products",
            status_code=500,
            error_code="ADMIN_PRODUCTS_FETCH_FAILED"
        )

@admin_bp.route('/products/<int:product_id>/status', methods=['PUT'])
@admin_required_v2()
@validate_content_type()
@rate_limit_v2(limit=30, window=300, per='user')
def update_product_status(user, admin_user, product_id):
    """
    Update product status (approve, reject, activate, deactivate).

    PUT /api/v1/admin/products/{product_id}/status
    """
    try:
        # Validate JSON
        is_valid, data = validate_json()
        if not is_valid:
            return data

        # Validate required fields
        required_fields = ['action']
        is_valid, error_resp = validate_required_fields(data, required_fields)
        if not is_valid:
            return error_resp

        action = data['action']
        reason = data.get('reason', '').strip()

        # Validate action
        valid_actions = ['approve', 'reject', 'activate', 'deactivate']
        if action not in valid_actions:
            return validation_error_response(
                errors={"action": [f"Must be one of: {', '.join(valid_actions)}"]},
                message="Invalid action"
            )

        from app import db, Product

        # Get product
        product = Product.query.get(product_id)
        if not product:
            return not_found_response("Product", product_id)

        old_status = getattr(product, 'status', 'active')
        old_is_active = product.is_active

        # Apply action
        if action == 'approve':
            product.status = 'approved'
            product.is_active = True
        elif action == 'reject':
            product.status = 'rejected'
            product.is_active = False
        elif action == 'activate':
            product.is_active = True
        elif action == 'deactivate':
            product.is_active = False

        product.updated_at = datetime.utcnow()

        # Log admin action
        from app import AdminActionLog
        action_log = AdminActionLog(
            admin_user_id=user.id,
            action_type='product_status_update',
            target_type='product',
            target_id=product_id,
            details={
                'action': action,
                'old_status': old_status,
                'new_status': getattr(product, 'status', 'active'),
                'old_is_active': old_is_active,
                'new_is_active': product.is_active,
                'reason': reason
            },
            created_at=datetime.utcnow()
        )

        db.session.add(action_log)
        db.session.commit()

        return success_response(
            data={
                "product_id": product_id,
                "action": action,
                "new_status": {
                    "status": getattr(product, 'status', 'active'),
                    "is_active": product.is_active
                },
                "updated_at": product.updated_at.isoformat()
            },
            message=f"Product {action} successful"
        )

    except Exception as e:
        logger.error(f"Update product status error: {e}")
        db.session.rollback()
        return error_response(
            message="Failed to update product status",
            status_code=500,
            error_code="ADMIN_PRODUCT_STATUS_UPDATE_FAILED"
        )

# Admin Order Management Endpoints

@admin_bp.route('/orders', methods=['GET'])
@admin_required_v2()
@rate_limit_v2(limit=60, window=60, per='user')
def get_orders(user, admin_user):
    """
    Get all orders with pagination and filtering.

    GET /api/v1/admin/orders
    """
    try:
        page = request.args.get('page', 1, type=int)
        per_page = request.args.get('per_page', 20, type=int)
        status = request.args.get('status')
        payment_status = request.args.get('payment_status')
        seller_id = request.args.get('seller_id', type=int)
        user_id = request.args.get('user_id', type=int)
        date_from = request.args.get('date_from')
        date_to = request.args.get('date_to')

        # Validate pagination
        page, per_page = validate_pagination(page, per_page, max_per_page=100)

        from app import db, Order, User, OrderItem, Product, Seller

        # Build query
        query = Order.query

        # Apply filters
        if status:
            query = query.filter_by(status=status)

        if payment_status:
            query = query.filter_by(payment_status=payment_status)

        if seller_id:
            # Filter orders containing products from specific seller
            query = query.join(OrderItem).join(Product).filter(Product.seller_id == seller_id)

        if user_id:
            query = query.filter_by(user_id=user_id)

        if date_from:
            try:
                from_date = datetime.strptime(date_from, '%Y-%m-%d')
                query = query.filter(Order.created_at >= from_date)
            except ValueError:
                return validation_error_response(
                    errors={"date_from": ["Invalid date format. Use YYYY-MM-DD"]},
                    message="Invalid date format"
                )

        if date_to:
            try:
                to_date = datetime.strptime(date_to, '%Y-%m-%d')
                query = query.filter(Order.created_at <= to_date)
            except ValueError:
                return validation_error_response(
                    errors={"date_to": ["Invalid date format. Use YYYY-MM-DD"]},
                    message="Invalid date format"
                )

        query = query.order_by(Order.created_at.desc())

        # Execute paginated query
        paginated_orders = query.paginate(
            page=page,
            per_page=per_page,
            error_out=False
        )

        # Format orders data
        orders_data = []
        for order in paginated_orders.items:
            # Get customer info
            customer = User.query.get(order.user_id) if order.user_id else None

            # Get order items count
            items_count = OrderItem.query.filter_by(order_id=order.id).count()

            orders_data.append({
                "id": order.id,
                "order_number": f"ORD-{order.id:06d}",
                "status": order.status,
                "payment_status": getattr(order, 'payment_status', 'pending'),
                "payment_method": getattr(order, 'payment_method', None),
                "total_amount": float(order.total_amount),
                "currency": "INR",
                "items_count": items_count,
                "customer": {
                    "id": customer.id if customer else None,
                    "name": f"{customer.first_name} {customer.last_name}" if customer else "Guest",
                    "email": customer.email if customer else getattr(order, 'guest_email', None)
                },
                "created_at": order.created_at.isoformat() if order.created_at else None,
                "updated_at": order.updated_at.isoformat() if order.updated_at else None
            })

        return paginated_response(
            data=orders_data,
            page=page,
            per_page=per_page,
            total=paginated_orders.total,
            message="Orders retrieved successfully",
            meta={
                "filters_applied": {
                    "status": status,
                    "payment_status": payment_status,
                    "seller_id": seller_id,
                    "user_id": user_id,
                    "date_from": date_from,
                    "date_to": date_to
                }
            }
        )

    except Exception as e:
        logger.error(f"Get orders error: {e}")
        return error_response(
            message="Failed to retrieve orders",
            status_code=500,
            error_code="ADMIN_ORDERS_FETCH_FAILED"
        )

@admin_bp.route('/orders/<int:order_id>', methods=['GET'])
@admin_required_v2()
@rate_limit_v2(limit=60, window=60, per='user')
def get_order_details(user, admin_user, order_id):
    """
    Get detailed order information.

    GET /api/v1/admin/orders/{order_id}
    """
    try:
        from app import db, Order, User, OrderItem, Product, Seller, Address

        # Get order
        order = Order.query.get(order_id)
        if not order:
            return not_found_response("Order", order_id)

        # Get customer info
        customer = User.query.get(order.user_id) if order.user_id else None

        # Get order items with product details
        order_items = db.session.query(OrderItem, Product, Seller).join(
            Product, OrderItem.product_id == Product.id
        ).outerjoin(
            Seller, Product.seller_id == Seller.id
        ).filter(OrderItem.order_id == order.id).all()

        items_data = []
        for order_item, product, seller in order_items:
            items_data.append({
                "id": order_item.id,
                "product": {
                    "id": product.id,
                    "name": product.name,
                    "sku": product.sku,
                    "category": product.category
                },
                "seller": {
                    "id": seller.id if seller else None,
                    "business_name": seller.business_name if seller else "Allora Direct"
                },
                "quantity": order_item.quantity,
                "unit_price": float(order_item.price),
                "total_price": float(order_item.price * order_item.quantity)
            })

        # Get shipping address
        shipping_address = None
        if hasattr(order, 'shipping_address_id') and order.shipping_address_id:
            address = Address.query.get(order.shipping_address_id)
            if address:
                shipping_address = {
                    "street": address.street,
                    "city": address.city,
                    "state": address.state,
                    "postal_code": address.postal_code,
                    "country": address.country
                }

        order_data = {
            "id": order.id,
            "order_number": f"ORD-{order.id:06d}",
            "status": order.status,
            "payment_status": getattr(order, 'payment_status', 'pending'),
            "payment_method": getattr(order, 'payment_method', None),
            "subtotal": float(getattr(order, 'subtotal', 0)),
            "tax_amount": float(getattr(order, 'tax_amount', 0)),
            "shipping_amount": float(getattr(order, 'shipping_amount', 0)),
            "total_amount": float(order.total_amount),
            "currency": "INR",
            "customer": {
                "id": customer.id if customer else None,
                "name": f"{customer.first_name} {customer.last_name}" if customer else "Guest",
                "email": customer.email if customer else getattr(order, 'guest_email', None),
                "phone": getattr(customer, 'phone', None) if customer else None
            },
            "shipping_address": shipping_address,
            "items": items_data,
            "tracking_number": getattr(order, 'tracking_number', None),
            "estimated_delivery": order.estimated_delivery.isoformat() if getattr(order, 'estimated_delivery', None) else None,
            "created_at": order.created_at.isoformat() if order.created_at else None,
            "updated_at": order.updated_at.isoformat() if order.updated_at else None
        }

        return success_response(
            data=order_data,
            message="Order details retrieved successfully"
        )

    except Exception as e:
        logger.error(f"Get order details error: {e}")
        return error_response(
            message="Failed to retrieve order details",
            status_code=500,
            error_code="ADMIN_ORDER_DETAILS_FETCH_FAILED"
        )

@admin_bp.route('/orders/<int:order_id>/status', methods=['PUT'])
@admin_required_v2()
@validate_content_type()
@rate_limit_v2(limit=30, window=300, per='user')
def update_order_status(user, admin_user, order_id):
    """
    Update order status.

    PUT /api/v1/admin/orders/{order_id}/status
    """
    try:
        # Validate JSON
        is_valid, data = validate_json()
        if not is_valid:
            return data

        # Validate required fields
        required_fields = ['status']
        is_valid, error_resp = validate_required_fields(data, required_fields)
        if not is_valid:
            return error_resp

        new_status = data['status']
        reason = data.get('reason', '').strip()
        tracking_number = data.get('tracking_number', '').strip()

        # Validate status
        valid_statuses = ['pending', 'confirmed', 'processing', 'shipped', 'delivered', 'cancelled', 'refunded']
        if new_status not in valid_statuses:
            return validation_error_response(
                errors={"status": [f"Must be one of: {', '.join(valid_statuses)}"]},
                message="Invalid status"
            )

        from app import db, Order

        # Get order
        order = Order.query.get(order_id)
        if not order:
            return not_found_response("Order", order_id)

        old_status = order.status
        order.status = new_status
        order.updated_at = datetime.utcnow()

        # Update tracking number if provided
        if tracking_number:
            order.tracking_number = tracking_number

        # Set estimated delivery for shipped orders
        if new_status == 'shipped' and not getattr(order, 'estimated_delivery', None):
            order.estimated_delivery = datetime.utcnow() + timedelta(days=7)

        # Log admin action
        from app import AdminActionLog
        action_log = AdminActionLog(
            admin_user_id=user.id,
            action_type='order_status_update',
            target_type='order',
            target_id=order_id,
            details={
                'old_status': old_status,
                'new_status': new_status,
                'reason': reason,
                'tracking_number': tracking_number
            },
            created_at=datetime.utcnow()
        )

        db.session.add(action_log)
        db.session.commit()

        return success_response(
            data={
                "order_id": order_id,
                "old_status": old_status,
                "new_status": new_status,
                "tracking_number": getattr(order, 'tracking_number', None),
                "estimated_delivery": order.estimated_delivery.isoformat() if getattr(order, 'estimated_delivery', None) else None,
                "updated_at": order.updated_at.isoformat()
            },
            message=f"Order status updated to {new_status}"
        )

    except Exception as e:
        logger.error(f"Update order status error: {e}")
        db.session.rollback()
        return error_response(
            message="Failed to update order status",
            status_code=500,
            error_code="ADMIN_ORDER_STATUS_UPDATE_FAILED"
        )

# Admin Analytics Endpoints

@admin_bp.route('/analytics/sales', methods=['GET'])
@admin_required_v2()
@rate_limit_v2(limit=60, window=60, per='user')
def get_sales_analytics(user, admin_user):
    """
    Get sales analytics and metrics.

    GET /api/v1/admin/analytics/sales
    """
    try:
        period = request.args.get('period', 'month')  # day, week, month, year
        date_from = request.args.get('date_from')
        date_to = request.args.get('date_to')

        # Validate period
        valid_periods = ['day', 'week', 'month', 'year']
        if period not in valid_periods:
            return validation_error_response(
                errors={"period": [f"Must be one of: {', '.join(valid_periods)}"]},
                message="Invalid period"
            )

        from app import db, Order, OrderItem, Product, User

        # Calculate date range
        today = datetime.utcnow().date()
        if date_from and date_to:
            try:
                start_date = datetime.strptime(date_from, '%Y-%m-%d').date()
                end_date = datetime.strptime(date_to, '%Y-%m-%d').date()
            except ValueError:
                return validation_error_response(
                    errors={"date": ["Invalid date format. Use YYYY-MM-DD"]},
                    message="Invalid date format"
                )
        else:
            if period == 'day':
                start_date = today
                end_date = today
            elif period == 'week':
                start_date = today - timedelta(days=7)
                end_date = today
            elif period == 'month':
                start_date = today - timedelta(days=30)
                end_date = today
            else:  # year
                start_date = today - timedelta(days=365)
                end_date = today

        # Get sales metrics
        completed_orders = Order.query.filter(
            Order.status.in_(['completed', 'delivered']),
            db.func.date(Order.created_at) >= start_date,
            db.func.date(Order.created_at) <= end_date
        )

        total_revenue = db.session.query(
            db.func.sum(Order.total_amount)
        ).filter(
            Order.status.in_(['completed', 'delivered']),
            db.func.date(Order.created_at) >= start_date,
            db.func.date(Order.created_at) <= end_date
        ).scalar() or 0.0

        total_orders = completed_orders.count()

        # Get top selling products
        top_products = db.session.query(
            Product.id,
            Product.name,
            db.func.sum(OrderItem.quantity).label('total_sold'),
            db.func.sum(OrderItem.quantity * OrderItem.price).label('total_revenue')
        ).join(OrderItem).join(Order).filter(
            Order.status.in_(['completed', 'delivered']),
            db.func.date(Order.created_at) >= start_date,
            db.func.date(Order.created_at) <= end_date
        ).group_by(Product.id, Product.name).order_by(
            db.func.sum(OrderItem.quantity).desc()
        ).limit(10).all()

        top_products_data = []
        for product in top_products:
            top_products_data.append({
                "product_id": product.id,
                "product_name": product.name,
                "total_sold": int(product.total_sold),
                "total_revenue": float(product.total_revenue)
            })

        # Calculate average order value
        avg_order_value = total_revenue / total_orders if total_orders > 0 else 0.0

        analytics_data = {
            "period": {
                "type": period,
                "start_date": start_date.isoformat(),
                "end_date": end_date.isoformat()
            },
            "metrics": {
                "total_revenue": float(total_revenue),
                "total_orders": total_orders,
                "average_order_value": round(avg_order_value, 2),
                "currency": "INR"
            },
            "top_products": top_products_data
        }

        return success_response(
            data=analytics_data,
            message="Sales analytics retrieved successfully"
        )

    except Exception as e:
        logger.error(f"Get sales analytics error: {e}")
        return error_response(
            message="Failed to retrieve sales analytics",
            status_code=500,
            error_code="ADMIN_SALES_ANALYTICS_FETCH_FAILED"
        )

@admin_bp.route('/marketplace/stats', methods=['GET'])
@admin_required_v2()
@rate_limit_v2(limit=60, window=60, per='user')
def get_marketplace_stats(user, admin_user):
    """
    Get marketplace overview statistics.

    GET /api/v1/admin/marketplace/stats
    """
    try:
        from app import db, User, Seller, Product, Order

        # Calculate date ranges
        today = datetime.utcnow().date()
        last_30_days = today - timedelta(days=30)

        # User statistics
        total_users = User.query.filter_by(is_active=True).count()
        new_users_30d = User.query.filter(
            User.created_at >= last_30_days,
            User.is_active == True
        ).count()

        # Seller statistics
        total_sellers = Seller.query.count()
        active_sellers = Seller.query.filter_by(status='approved', is_verified=True).count()
        pending_sellers = Seller.query.filter_by(status='pending').count()

        # Product statistics
        total_products = Product.query.count()
        active_products = Product.query.filter_by(is_active=True).count()
        pending_products = Product.query.filter_by(status='pending').count()

        # Order statistics
        total_orders = Order.query.count()
        completed_orders = Order.query.filter(Order.status.in_(['completed', 'delivered'])).count()
        pending_orders = Order.query.filter_by(status='pending').count()

        # Revenue statistics
        total_revenue = db.session.query(
            db.func.sum(Order.total_amount)
        ).filter(Order.status.in_(['completed', 'delivered'])).scalar() or 0.0

        revenue_30d = db.session.query(
            db.func.sum(Order.total_amount)
        ).filter(
            Order.status.in_(['completed', 'delivered']),
            Order.created_at >= last_30_days
        ).scalar() or 0.0

        marketplace_stats = {
            "users": {
                "total": total_users,
                "new_last_30_days": new_users_30d,
                "growth_rate": round((new_users_30d / max(total_users - new_users_30d, 1)) * 100, 2)
            },
            "sellers": {
                "total": total_sellers,
                "active": active_sellers,
                "pending_approval": pending_sellers,
                "approval_rate": round((active_sellers / max(total_sellers, 1)) * 100, 2)
            },
            "products": {
                "total": total_products,
                "active": active_products,
                "pending_approval": pending_products,
                "approval_rate": round((active_products / max(total_products, 1)) * 100, 2)
            },
            "orders": {
                "total": total_orders,
                "completed": completed_orders,
                "pending": pending_orders,
                "completion_rate": round((completed_orders / max(total_orders, 1)) * 100, 2)
            },
            "revenue": {
                "total": float(total_revenue),
                "last_30_days": float(revenue_30d),
                "currency": "INR",
                "average_order_value": round(total_revenue / max(completed_orders, 1), 2)
            }
        }

        return success_response(
            data=marketplace_stats,
            message="Marketplace statistics retrieved successfully"
        )

    except Exception as e:
        logger.error(f"Get marketplace stats error: {e}")
        return error_response(
            message="Failed to retrieve marketplace statistics",
            status_code=500,
            error_code="ADMIN_MARKETPLACE_STATS_FETCH_FAILED"
        )

# Admin Inventory Management Endpoints

@admin_bp.route('/inventory', methods=['GET'])
@admin_required_v2()
@rate_limit_v2(limit=60, window=60, per='user')
def get_inventory_overview(user, admin_user):
    """
    Get inventory overview and low stock alerts.

    GET /api/v1/admin/inventory
    """
    try:
        low_stock_threshold = request.args.get('low_stock_threshold', 10, type=int)
        page = request.args.get('page', 1, type=int)
        per_page = request.args.get('per_page', 20, type=int)

        # Validate pagination
        page, per_page = validate_pagination(page, per_page, max_per_page=100)

        from app import db, Product, Seller

        # Get low stock products
        low_stock_query = Product.query.filter(
            Product.is_active == True,
            Product.stock_quantity <= low_stock_threshold
        ).order_by(Product.stock_quantity.asc())

        # Execute paginated query
        paginated_products = low_stock_query.paginate(
            page=page,
            per_page=per_page,
            error_out=False
        )

        # Format low stock products
        low_stock_products = []
        for product in paginated_products.items:
            seller = Seller.query.get(product.seller_id) if product.seller_id else None

            low_stock_products.append({
                "id": product.id,
                "name": product.name,
                "sku": product.sku,
                "current_stock": product.stock_quantity,
                "category": product.category,
                "price": float(product.price),
                "seller": {
                    "id": seller.id if seller else None,
                    "business_name": seller.business_name if seller else "Allora Direct"
                },
                "last_updated": product.updated_at.isoformat() if product.updated_at else None
            })

        # Get inventory statistics
        total_products = Product.query.filter_by(is_active=True).count()
        out_of_stock = Product.query.filter(
            Product.is_active == True,
            Product.stock_quantity == 0
        ).count()
        low_stock_count = Product.query.filter(
            Product.is_active == True,
            Product.stock_quantity > 0,
            Product.stock_quantity <= low_stock_threshold
        ).count()

        total_stock_value = db.session.query(
            db.func.sum(Product.price * Product.stock_quantity)
        ).filter(Product.is_active == True).scalar() or 0.0

        inventory_data = {
            "statistics": {
                "total_products": total_products,
                "out_of_stock": out_of_stock,
                "low_stock": low_stock_count,
                "total_stock_value": float(total_stock_value),
                "low_stock_threshold": low_stock_threshold
            },
            "low_stock_products": low_stock_products,
            "pagination": {
                "page": page,
                "per_page": per_page,
                "total": paginated_products.total,
                "pages": paginated_products.pages,
                "has_next": paginated_products.has_next,
                "has_prev": paginated_products.has_prev
            }
        }

        return success_response(
            data=inventory_data,
            message="Inventory overview retrieved successfully"
        )

    except Exception as e:
        logger.error(f"Get inventory overview error: {e}")
        return error_response(
            message="Failed to retrieve inventory overview",
            status_code=500,
            error_code="ADMIN_INVENTORY_OVERVIEW_FETCH_FAILED"
        )

@admin_bp.route('/inventory/<int:product_id>/adjust', methods=['POST'])
@admin_required_v2()
@validate_content_type()
@rate_limit_v2(limit=30, window=300, per='user')
def adjust_inventory(user, admin_user, product_id):
    """
    Adjust product inventory levels.

    POST /api/v1/admin/inventory/{product_id}/adjust
    """
    try:
        # Validate JSON
        is_valid, data = validate_json()
        if not is_valid:
            return data

        # Validate required fields
        required_fields = ['adjustment_type', 'quantity']
        is_valid, error_resp = validate_required_fields(data, required_fields)
        if not is_valid:
            return error_resp

        adjustment_type = data['adjustment_type']  # add, subtract, set
        quantity = data['quantity']
        reason = data.get('reason', '').strip()

        # Validate adjustment type
        valid_types = ['add', 'subtract', 'set']
        if adjustment_type not in valid_types:
            return validation_error_response(
                errors={"adjustment_type": [f"Must be one of: {', '.join(valid_types)}"]},
                message="Invalid adjustment type"
            )

        # Validate quantity
        if not isinstance(quantity, int) or quantity < 0:
            return validation_error_response(
                errors={"quantity": ["Quantity must be a non-negative integer"]},
                message="Invalid quantity"
            )

        from app import db, Product

        # Get product
        product = Product.query.get(product_id)
        if not product:
            return not_found_response("Product", product_id)

        old_stock = product.stock_quantity

        # Apply adjustment
        if adjustment_type == 'add':
            new_stock = old_stock + quantity
        elif adjustment_type == 'subtract':
            new_stock = max(0, old_stock - quantity)  # Don't allow negative stock
        else:  # set
            new_stock = quantity

        product.stock_quantity = new_stock
        product.updated_at = datetime.utcnow()

        # Log admin action
        from app import AdminActionLog
        action_log = AdminActionLog(
            admin_user_id=user.id,
            action_type='inventory_adjustment',
            target_type='product',
            target_id=product_id,
            details={
                'adjustment_type': adjustment_type,
                'quantity': quantity,
                'old_stock': old_stock,
                'new_stock': new_stock,
                'reason': reason
            },
            created_at=datetime.utcnow()
        )

        db.session.add(action_log)
        db.session.commit()

        return success_response(
            data={
                "product_id": product_id,
                "adjustment_type": adjustment_type,
                "quantity": quantity,
                "old_stock": old_stock,
                "new_stock": new_stock,
                "updated_at": product.updated_at.isoformat()
            },
            message="Inventory adjusted successfully"
        )

    except Exception as e:
        logger.error(f"Adjust inventory error: {e}")
        db.session.rollback()
        return error_response(
            message="Failed to adjust inventory",
            status_code=500,
            error_code="ADMIN_INVENTORY_ADJUST_FAILED"
        )

# Admin System Management Endpoints

@admin_bp.route('/inventory/stats', methods=['GET'])
@admin_required_v2()
@rate_limit_v2(limit=60, window=60, per='user')
def get_inventory_sync_stats(user, admin_user):
    """
    Get inventory synchronization statistics.

    GET /api/v1/admin/inventory/stats
    """
    try:
        from app import db, Product, InventorySyncLog

        # Get sync statistics
        total_syncs = InventorySyncLog.query.count() if hasattr(db, 'InventorySyncLog') else 0
        successful_syncs = InventorySyncLog.query.filter_by(status='success').count() if hasattr(db, 'InventorySyncLog') else 0
        failed_syncs = InventorySyncLog.query.filter_by(status='failed').count() if hasattr(db, 'InventorySyncLog') else 0

        # Get recent sync logs
        recent_syncs = []
        if hasattr(db, 'InventorySyncLog'):
            recent_sync_logs = InventorySyncLog.query.order_by(
                InventorySyncLog.created_at.desc()
            ).limit(10).all()

            for sync_log in recent_sync_logs:
                recent_syncs.append({
                    "id": sync_log.id,
                    "status": sync_log.status,
                    "channel": getattr(sync_log, 'channel', 'unknown'),
                    "products_synced": getattr(sync_log, 'products_synced', 0),
                    "errors": getattr(sync_log, 'errors', []),
                    "created_at": sync_log.created_at.isoformat() if sync_log.created_at else None
                })

        # Get product sync status
        total_products = Product.query.count()
        synced_products = Product.query.filter(
            Product.last_sync_at.isnot(None)
        ).count() if hasattr(Product, 'last_sync_at') else total_products

        sync_stats = {
            "sync_statistics": {
                "total_syncs": total_syncs,
                "successful_syncs": successful_syncs,
                "failed_syncs": failed_syncs,
                "success_rate": round((successful_syncs / max(total_syncs, 1)) * 100, 2)
            },
            "product_sync_status": {
                "total_products": total_products,
                "synced_products": synced_products,
                "sync_coverage": round((synced_products / max(total_products, 1)) * 100, 2)
            },
            "recent_syncs": recent_syncs
        }

        return success_response(
            data=sync_stats,
            message="Inventory sync statistics retrieved successfully"
        )

    except Exception as e:
        logger.error(f"Get inventory sync stats error: {e}")
        return error_response(
            message="Failed to retrieve inventory sync statistics",
            status_code=500,
            error_code="ADMIN_INVENTORY_SYNC_STATS_FETCH_FAILED"
        )

@admin_bp.route('/scheduler/status', methods=['GET'])
@admin_required_v2()
@rate_limit_v2(limit=60, window=60, per='user')
def get_scheduler_status(user, admin_user):
    """
    Get scheduler status and job information.

    GET /api/v1/admin/scheduler/status
    """
    try:
        # Mock scheduler status - replace with actual scheduler integration
        scheduler_status = {
            "is_running": True,  # This would come from actual scheduler
            "jobs": [
                {
                    "id": "inventory_sync",
                    "name": "Inventory Synchronization",
                    "status": "active",
                    "next_run": "2024-01-01T12:00:00Z",
                    "last_run": "2024-01-01T11:00:00Z",
                    "interval": "1 hour"
                },
                {
                    "id": "order_cleanup",
                    "name": "Order Cleanup",
                    "status": "active",
                    "next_run": "2024-01-02T00:00:00Z",
                    "last_run": "2024-01-01T00:00:00Z",
                    "interval": "1 day"
                }
            ],
            "system_info": {
                "uptime": "5 days, 12 hours",
                "memory_usage": "45%",
                "cpu_usage": "12%"
            }
        }

        return success_response(
            data=scheduler_status,
            message="Scheduler status retrieved successfully"
        )

    except Exception as e:
        logger.error(f"Get scheduler status error: {e}")
        return error_response(
            message="Failed to retrieve scheduler status",
            status_code=500,
            error_code="ADMIN_SCHEDULER_STATUS_FETCH_FAILED"
        )

@admin_bp.route('/scheduler/start', methods=['POST'])
@admin_required_v2()
@rate_limit_v2(limit=10, window=300, per='user')
def start_scheduler(user, admin_user):
    """
    Start the inventory sync scheduler.

    POST /api/v1/admin/scheduler/start
    """
    try:
        # Mock scheduler start - replace with actual scheduler integration
        # In a real implementation, this would start the actual scheduler service

        # Log admin action
        from app import db, AdminActionLog
        action_log = AdminActionLog(
            admin_user_id=user.id,
            action_type='scheduler_start',
            target_type='system',
            target_id=None,
            details={
                'action': 'start_scheduler',
                'timestamp': datetime.utcnow().isoformat()
            },
            created_at=datetime.utcnow()
        )

        db.session.add(action_log)
        db.session.commit()

        return success_response(
            data={
                "scheduler_status": "started",
                "started_at": datetime.utcnow().isoformat(),
                "started_by": f"{user.first_name} {user.last_name}"
            },
            message="Scheduler started successfully"
        )

    except Exception as e:
        logger.error(f"Start scheduler error: {e}")
        db.session.rollback()
        return error_response(
            message="Failed to start scheduler",
            status_code=500,
            error_code="ADMIN_SCHEDULER_START_FAILED"
        )

@admin_bp.route('/scheduler/stop', methods=['POST'])
@admin_required_v2()
@rate_limit_v2(limit=10, window=300, per='user')
def stop_scheduler(user, admin_user):
    """
    Stop the inventory sync scheduler.

    POST /api/v1/admin/scheduler/stop
    """
    try:
        # Mock scheduler stop - replace with actual scheduler integration
        # In a real implementation, this would stop the actual scheduler service

        # Log admin action
        from app import db, AdminActionLog
        action_log = AdminActionLog(
            admin_user_id=user.id,
            action_type='scheduler_stop',
            target_type='system',
            target_id=None,
            details={
                'action': 'stop_scheduler',
                'timestamp': datetime.utcnow().isoformat()
            },
            created_at=datetime.utcnow()
        )

        db.session.add(action_log)
        db.session.commit()

        return success_response(
            data={
                "scheduler_status": "stopped",
                "stopped_at": datetime.utcnow().isoformat(),
                "stopped_by": f"{user.first_name} {user.last_name}"
            },
            message="Scheduler stopped successfully"
        )

    except Exception as e:
        logger.error(f"Stop scheduler error: {e}")
        db.session.rollback()
        return error_response(
            message="Failed to stop scheduler",
            status_code=500,
            error_code="ADMIN_SCHEDULER_STOP_FAILED"
        )

# Admin Content Management Endpoints

@admin_bp.route('/content', methods=['GET'])
@admin_required_v2()
@rate_limit_v2(limit=60, window=60, per='user')
def get_content_pages(user, admin_user):
    """
    Get all content pages with pagination.

    GET /api/v1/admin/content
    """
    try:
        page = request.args.get('page', 1, type=int)
        per_page = request.args.get('per_page', 20, type=int)
        status = request.args.get('status')  # published, draft, archived
        content_type = request.args.get('type')  # page, blog, faq, etc.
        search = request.args.get('search', '').strip()

        # Validate pagination
        page, per_page = validate_pagination(page, per_page, max_per_page=100)

        from app import db, ContentPage

        # Build query
        query = ContentPage.query if hasattr(db, 'ContentPage') else []

        if hasattr(db, 'ContentPage'):
            # Apply filters
            if status:
                query = query.filter_by(status=status)

            if content_type:
                query = query.filter_by(content_type=content_type)

            if search:
                query = query.filter(
                    db.or_(
                        ContentPage.title.ilike(f'%{search}%'),
                        ContentPage.slug.ilike(f'%{search}%'),
                        ContentPage.content.ilike(f'%{search}%')
                    )
                )

            query = query.order_by(ContentPage.updated_at.desc())

            # Execute paginated query
            paginated_content = query.paginate(
                page=page,
                per_page=per_page,
                error_out=False
            )

            # Format content data
            content_data = []
            for content in paginated_content.items:
                content_data.append({
                    "id": content.id,
                    "title": content.title,
                    "slug": content.slug,
                    "content_type": getattr(content, 'content_type', 'page'),
                    "status": getattr(content, 'status', 'draft'),
                    "author": getattr(content, 'author', 'Admin'),
                    "meta_description": getattr(content, 'meta_description', None),
                    "is_featured": getattr(content, 'is_featured', False),
                    "view_count": getattr(content, 'view_count', 0),
                    "created_at": content.created_at.isoformat() if content.created_at else None,
                    "updated_at": content.updated_at.isoformat() if content.updated_at else None,
                    "published_at": getattr(content, 'published_at', None)
                })

            return paginated_response(
                data=content_data,
                page=page,
                per_page=per_page,
                total=paginated_content.total,
                message="Content pages retrieved successfully",
                meta={
                    "filters_applied": {
                        "status": status,
                        "content_type": content_type,
                        "search": search
                    }
                }
            )
        else:
            # Mock response if ContentPage model doesn't exist
            return paginated_response(
                data=[],
                page=page,
                per_page=per_page,
                total=0,
                message="Content management system not configured"
            )

    except Exception as e:
        logger.error(f"Get content pages error: {e}")
        return error_response(
            message="Failed to retrieve content pages",
            status_code=500,
            error_code="ADMIN_CONTENT_FETCH_FAILED"
        )

@admin_bp.route('/content', methods=['POST'])
@admin_required_v2()
@validate_content_type()
@rate_limit_v2(limit=20, window=3600, per='user')
def create_content_page(user, admin_user):
    """
    Create a new content page.

    POST /api/v1/admin/content
    """
    try:
        # Validate JSON
        is_valid, data = validate_json()
        if not is_valid:
            return data

        # Validate required fields
        required_fields = ['title', 'content']
        is_valid, error_resp = validate_required_fields(data, required_fields)
        if not is_valid:
            return error_resp

        title = data['title'].strip()
        content = data['content'].strip()
        slug = data.get('slug', '').strip() or title.lower().replace(' ', '-').replace('_', '-')
        content_type = data.get('content_type', 'page')
        status = data.get('status', 'draft')
        meta_description = data.get('meta_description', '').strip()
        is_featured = data.get('is_featured', False)

        # Validate content type
        valid_types = ['page', 'blog', 'faq', 'policy', 'help']
        if content_type not in valid_types:
            return validation_error_response(
                errors={"content_type": [f"Must be one of: {', '.join(valid_types)}"]},
                message="Invalid content type"
            )

        # Validate status
        valid_statuses = ['draft', 'published', 'archived']
        if status not in valid_statuses:
            return validation_error_response(
                errors={"status": [f"Must be one of: {', '.join(valid_statuses)}"]},
                message="Invalid status"
            )

        from app import db, ContentPage

        if hasattr(db, 'ContentPage'):
            # Check if slug already exists
            existing_content = ContentPage.query.filter_by(slug=slug).first()
            if existing_content:
                return error_response(
                    message="Content with this slug already exists",
                    status_code=409,
                    error_code="CONTENT_SLUG_EXISTS"
                )

            # Create new content page
            new_content = ContentPage(
                title=title,
                slug=slug,
                content=content,
                content_type=content_type,
                status=status,
                meta_description=meta_description or None,
                is_featured=is_featured,
                author=f"{user.first_name} {user.last_name}",
                view_count=0,
                created_at=datetime.utcnow(),
                updated_at=datetime.utcnow(),
                published_at=datetime.utcnow() if status == 'published' else None
            )

            db.session.add(new_content)
            db.session.commit()

            content_data = {
                "id": new_content.id,
                "title": new_content.title,
                "slug": new_content.slug,
                "content_type": new_content.content_type,
                "status": new_content.status,
                "created_at": new_content.created_at.isoformat()
            }

            return success_response(
                data=content_data,
                message="Content page created successfully",
                status_code=201
            )
        else:
            return error_response(
                message="Content management system not configured",
                status_code=501,
                error_code="CMS_NOT_CONFIGURED"
            )

    except Exception as e:
        logger.error(f"Create content page error: {e}")
        db.session.rollback()
        return error_response(
            message="Failed to create content page",
            status_code=500,
            error_code="ADMIN_CONTENT_CREATE_FAILED"
        )

@admin_bp.route('/content/<int:content_id>', methods=['GET'])
@admin_required_v2()
@rate_limit_v2(limit=60, window=60, per='user')
def get_content_page_details(user, admin_user, content_id):
    """
    Get detailed content page information.

    GET /api/v1/admin/content/{content_id}
    """
    try:
        from app import db, ContentPage

        if hasattr(db, 'ContentPage'):
            # Get content page
            content = ContentPage.query.get(content_id)
            if not content:
                return not_found_response("Content page", content_id)

            content_data = {
                "id": content.id,
                "title": content.title,
                "slug": content.slug,
                "content": content.content,
                "content_type": getattr(content, 'content_type', 'page'),
                "status": getattr(content, 'status', 'draft'),
                "author": getattr(content, 'author', 'Admin'),
                "meta_description": getattr(content, 'meta_description', None),
                "meta_keywords": getattr(content, 'meta_keywords', None),
                "is_featured": getattr(content, 'is_featured', False),
                "view_count": getattr(content, 'view_count', 0),
                "seo_title": getattr(content, 'seo_title', None),
                "created_at": content.created_at.isoformat() if content.created_at else None,
                "updated_at": content.updated_at.isoformat() if content.updated_at else None,
                "published_at": getattr(content, 'published_at', None)
            }

            return success_response(
                data=content_data,
                message="Content page details retrieved successfully"
            )
        else:
            return error_response(
                message="Content management system not configured",
                status_code=501,
                error_code="CMS_NOT_CONFIGURED"
            )

    except Exception as e:
        logger.error(f"Get content page details error: {e}")
        return error_response(
            message="Failed to retrieve content page details",
            status_code=500,
            error_code="ADMIN_CONTENT_DETAILS_FETCH_FAILED"
        )

@admin_bp.route('/content/<int:content_id>', methods=['PUT'])
@admin_required_v2()
@validate_content_type()
@rate_limit_v2(limit=30, window=3600, per='user')
def update_content_page(user, admin_user, content_id):
    """
    Update content page.

    PUT /api/v1/admin/content/{content_id}
    """
    try:
        # Validate JSON
        is_valid, data = validate_json()
        if not is_valid:
            return data

        from app import db, ContentPage

        if hasattr(db, 'ContentPage'):
            # Get content page
            content = ContentPage.query.get(content_id)
            if not content:
                return not_found_response("Content page", content_id)

            # Fields that can be updated
            updatable_fields = {
                'title': str,
                'content': str,
                'slug': str,
                'content_type': str,
                'status': str,
                'meta_description': str,
                'meta_keywords': str,
                'is_featured': bool,
                'seo_title': str
            }

            updated_fields = []

            for field, field_type in updatable_fields.items():
                if field in data:
                    value = data[field]

                    # Validate field type
                    if value is not None:
                        if field_type == str and not isinstance(value, str):
                            value = str(value).strip()
                        elif field_type == bool and not isinstance(value, bool):
                            value = bool(value)

                    # Special validation
                    if field == 'content_type' and value:
                        valid_types = ['page', 'blog', 'faq', 'policy', 'help']
                        if value not in valid_types:
                            return validation_error_response(
                                errors={"content_type": [f"Must be one of: {', '.join(valid_types)}"]},
                                message="Invalid content type"
                            )

                    if field == 'status' and value:
                        valid_statuses = ['draft', 'published', 'archived']
                        if value not in valid_statuses:
                            return validation_error_response(
                                errors={"status": [f"Must be one of: {', '.join(valid_statuses)}"]},
                                message="Invalid status"
                            )

                        # Set published_at when publishing
                        if value == 'published' and getattr(content, 'status', 'draft') != 'published':
                            content.published_at = datetime.utcnow()

                    if field == 'slug' and value:
                        # Check if slug already exists (excluding current content)
                        existing_content = ContentPage.query.filter(
                            ContentPage.slug == value,
                            ContentPage.id != content_id
                        ).first()
                        if existing_content:
                            return error_response(
                                message="Content with this slug already exists",
                                status_code=409,
                                error_code="CONTENT_SLUG_EXISTS"
                            )

                    # Update content field
                    setattr(content, field, value)
                    updated_fields.append(field)

            if updated_fields:
                content.updated_at = datetime.utcnow()
                db.session.commit()

                return success_response(
                    data={
                        "updated_fields": updated_fields,
                        "content_id": content_id,
                        "updated_at": content.updated_at.isoformat()
                    },
                    message="Content page updated successfully"
                )
            else:
                return success_response(
                    data={"updated_fields": []},
                    message="No changes made to content page"
                )
        else:
            return error_response(
                message="Content management system not configured",
                status_code=501,
                error_code="CMS_NOT_CONFIGURED"
            )

    except Exception as e:
        logger.error(f"Update content page error: {e}")
        db.session.rollback()
        return error_response(
            message="Failed to update content page",
            status_code=500,
            error_code="ADMIN_CONTENT_UPDATE_FAILED"
        )

@admin_bp.route('/content/<int:content_id>', methods=['DELETE'])
@admin_required_v2()
@rate_limit_v2(limit=20, window=3600, per='user')
def delete_content_page(user, admin_user, content_id):
    """
    Delete content page.

    DELETE /api/v1/admin/content/{content_id}
    """
    try:
        from app import db, ContentPage

        if hasattr(db, 'ContentPage'):
            # Get content page
            content = ContentPage.query.get(content_id)
            if not content:
                return not_found_response("Content page", content_id)

            # Log admin action before deletion
            from app import AdminActionLog
            action_log = AdminActionLog(
                admin_user_id=user.id,
                action_type='content_deletion',
                target_type='content',
                target_id=content_id,
                details={
                    'title': content.title,
                    'slug': content.slug,
                    'content_type': getattr(content, 'content_type', 'page')
                },
                created_at=datetime.utcnow()
            )

            db.session.add(action_log)
            db.session.delete(content)
            db.session.commit()

            return success_response(
                data={"content_id": content_id},
                message="Content page deleted successfully"
            )
        else:
            return error_response(
                message="Content management system not configured",
                status_code=501,
                error_code="CMS_NOT_CONFIGURED"
            )

    except Exception as e:
        logger.error(f"Delete content page error: {e}")
        db.session.rollback()
        return error_response(
            message="Failed to delete content page",
            status_code=500,
            error_code="ADMIN_CONTENT_DELETE_FAILED"
        )

# Admin Sales Channel Management Endpoints

@admin_bp.route('/channels', methods=['GET'])
@admin_required_v2()
@rate_limit_v2(limit=60, window=60, per='user')
def get_sales_channels(user, admin_user):
    """
    Get all sales channels with pagination.

    GET /api/v1/admin/channels
    """
    try:
        page = request.args.get('page', 1, type=int)
        per_page = request.args.get('per_page', 20, type=int)
        status = request.args.get('status')  # active, inactive, error
        channel_type = request.args.get('type')  # marketplace, social, direct

        # Validate pagination
        page, per_page = validate_pagination(page, per_page, max_per_page=100)

        from app import db, SalesChannel

        if hasattr(db, 'SalesChannel'):
            # Build query
            query = SalesChannel.query

            # Apply filters
            if status:
                query = query.filter_by(status=status)

            if channel_type:
                query = query.filter_by(channel_type=channel_type)

            query = query.order_by(SalesChannel.created_at.desc())

            # Execute paginated query
            paginated_channels = query.paginate(
                page=page,
                per_page=per_page,
                error_out=False
            )

            # Format channels data
            channels_data = []
            for channel in paginated_channels.items:
                channels_data.append({
                    "id": channel.id,
                    "name": channel.name,
                    "channel_type": getattr(channel, 'channel_type', 'marketplace'),
                    "status": getattr(channel, 'status', 'inactive'),
                    "api_endpoint": getattr(channel, 'api_endpoint', None),
                    "sync_enabled": getattr(channel, 'sync_enabled', False),
                    "last_sync": getattr(channel, 'last_sync', None),
                    "products_synced": getattr(channel, 'products_synced', 0),
                    "orders_imported": getattr(channel, 'orders_imported', 0),
                    "created_at": channel.created_at.isoformat() if channel.created_at else None,
                    "updated_at": channel.updated_at.isoformat() if channel.updated_at else None
                })

            return paginated_response(
                data=channels_data,
                page=page,
                per_page=per_page,
                total=paginated_channels.total,
                message="Sales channels retrieved successfully",
                meta={
                    "filters_applied": {
                        "status": status,
                        "channel_type": channel_type
                    }
                }
            )
        else:
            # Mock response if SalesChannel model doesn't exist
            mock_channels = [
                {
                    "id": 1,
                    "name": "Amazon",
                    "channel_type": "marketplace",
                    "status": "active",
                    "sync_enabled": True,
                    "products_synced": 150,
                    "orders_imported": 45
                },
                {
                    "id": 2,
                    "name": "Flipkart",
                    "channel_type": "marketplace",
                    "status": "active",
                    "sync_enabled": True,
                    "products_synced": 120,
                    "orders_imported": 32
                }
            ]

            return paginated_response(
                data=mock_channels,
                page=page,
                per_page=per_page,
                total=len(mock_channels),
                message="Sales channels retrieved successfully (mock data)"
            )

    except Exception as e:
        logger.error(f"Get sales channels error: {e}")
        return error_response(
            message="Failed to retrieve sales channels",
            status_code=500,
            error_code="ADMIN_CHANNELS_FETCH_FAILED"
        )

@admin_bp.route('/channels', methods=['POST'])
@admin_required_v2()
@validate_content_type()
@rate_limit_v2(limit=10, window=3600, per='user')
def create_sales_channel(user, admin_user):
    """
    Create a new sales channel.

    POST /api/v1/admin/channels
    """
    try:
        # Validate JSON
        is_valid, data = validate_json()
        if not is_valid:
            return data

        # Validate required fields
        required_fields = ['name', 'channel_type']
        is_valid, error_resp = validate_required_fields(data, required_fields)
        if not is_valid:
            return error_resp

        name = data['name'].strip()
        channel_type = data['channel_type']
        api_endpoint = data.get('api_endpoint', '').strip()
        api_key = data.get('api_key', '').strip()
        sync_enabled = data.get('sync_enabled', False)

        # Validate channel type
        valid_types = ['marketplace', 'social', 'direct', 'wholesale']
        if channel_type not in valid_types:
            return validation_error_response(
                errors={"channel_type": [f"Must be one of: {', '.join(valid_types)}"]},
                message="Invalid channel type"
            )

        from app import db, SalesChannel

        if hasattr(db, 'SalesChannel'):
            # Check if channel already exists
            existing_channel = SalesChannel.query.filter_by(name=name).first()
            if existing_channel:
                return error_response(
                    message="Sales channel with this name already exists",
                    status_code=409,
                    error_code="CHANNEL_EXISTS"
                )

            # Create new sales channel
            new_channel = SalesChannel(
                name=name,
                channel_type=channel_type,
                api_endpoint=api_endpoint or None,
                api_key=api_key or None,
                sync_enabled=sync_enabled,
                status='inactive',  # Start as inactive
                products_synced=0,
                orders_imported=0,
                created_at=datetime.utcnow(),
                updated_at=datetime.utcnow()
            )

            db.session.add(new_channel)
            db.session.commit()

            channel_data = {
                "id": new_channel.id,
                "name": new_channel.name,
                "channel_type": new_channel.channel_type,
                "status": new_channel.status,
                "sync_enabled": new_channel.sync_enabled,
                "created_at": new_channel.created_at.isoformat()
            }

            return success_response(
                data=channel_data,
                message="Sales channel created successfully",
                status_code=201
            )
        else:
            return error_response(
                message="Sales channel management not configured",
                status_code=501,
                error_code="CHANNEL_MANAGEMENT_NOT_CONFIGURED"
            )

    except Exception as e:
        logger.error(f"Create sales channel error: {e}")
        db.session.rollback()
        return error_response(
            message="Failed to create sales channel",
            status_code=500,
            error_code="ADMIN_CHANNEL_CREATE_FAILED"
        )

@admin_bp.route('/channels/<int:channel_id>', methods=['PUT'])
@admin_required_v2()
@validate_content_type()
@rate_limit_v2(limit=30, window=3600, per='user')
def update_sales_channel(user, admin_user, channel_id):
    """
    Update sales channel configuration.

    PUT /api/v1/admin/channels/{channel_id}
    """
    try:
        # Validate JSON
        is_valid, data = validate_json()
        if not is_valid:
            return data

        from app import db, SalesChannel

        if hasattr(db, 'SalesChannel'):
            # Get sales channel
            channel = SalesChannel.query.get(channel_id)
            if not channel:
                return not_found_response("Sales channel", channel_id)

            # Fields that can be updated
            updatable_fields = {
                'name': str,
                'channel_type': str,
                'api_endpoint': str,
                'api_key': str,
                'sync_enabled': bool,
                'status': str
            }

            updated_fields = []

            for field, field_type in updatable_fields.items():
                if field in data:
                    value = data[field]

                    # Validate field type
                    if value is not None:
                        if field_type == str and not isinstance(value, str):
                            value = str(value).strip()
                        elif field_type == bool and not isinstance(value, bool):
                            value = bool(value)

                    # Special validation
                    if field == 'channel_type' and value:
                        valid_types = ['marketplace', 'social', 'direct', 'wholesale']
                        if value not in valid_types:
                            return validation_error_response(
                                errors={"channel_type": [f"Must be one of: {', '.join(valid_types)}"]},
                                message="Invalid channel type"
                            )

                    if field == 'status' and value:
                        valid_statuses = ['active', 'inactive', 'error', 'maintenance']
                        if value not in valid_statuses:
                            return validation_error_response(
                                errors={"status": [f"Must be one of: {', '.join(valid_statuses)}"]},
                                message="Invalid status"
                            )

                    if field == 'name' and value:
                        # Check if name already exists (excluding current channel)
                        existing_channel = SalesChannel.query.filter(
                            SalesChannel.name == value,
                            SalesChannel.id != channel_id
                        ).first()
                        if existing_channel:
                            return error_response(
                                message="Sales channel with this name already exists",
                                status_code=409,
                                error_code="CHANNEL_NAME_EXISTS"
                            )

                    # Update channel field
                    setattr(channel, field, value)
                    updated_fields.append(field)

            if updated_fields:
                channel.updated_at = datetime.utcnow()
                db.session.commit()

                return success_response(
                    data={
                        "updated_fields": updated_fields,
                        "channel_id": channel_id,
                        "updated_at": channel.updated_at.isoformat()
                    },
                    message="Sales channel updated successfully"
                )
            else:
                return success_response(
                    data={"updated_fields": []},
                    message="No changes made to sales channel"
                )
        else:
            return error_response(
                message="Sales channel management not configured",
                status_code=501,
                error_code="CHANNEL_MANAGEMENT_NOT_CONFIGURED"
            )

    except Exception as e:
        logger.error(f"Update sales channel error: {e}")
        db.session.rollback()
        return error_response(
            message="Failed to update sales channel",
            status_code=500,
            error_code="ADMIN_CHANNEL_UPDATE_FAILED"
        )

@admin_bp.route('/channels/<int:channel_id>', methods=['DELETE'])
@admin_required_v2()
@rate_limit_v2(limit=10, window=3600, per='user')
def delete_sales_channel(user, admin_user, channel_id):
    """
    Delete sales channel.

    DELETE /api/v1/admin/channels/{channel_id}
    """
    try:
        from app import db, SalesChannel

        if hasattr(db, 'SalesChannel'):
            # Get sales channel
            channel = SalesChannel.query.get(channel_id)
            if not channel:
                return not_found_response("Sales channel", channel_id)

            # Log admin action before deletion
            from app import AdminActionLog
            action_log = AdminActionLog(
                admin_user_id=user.id,
                action_type='channel_deletion',
                target_type='channel',
                target_id=channel_id,
                details={
                    'name': channel.name,
                    'channel_type': getattr(channel, 'channel_type', 'unknown'),
                    'products_synced': getattr(channel, 'products_synced', 0)
                },
                created_at=datetime.utcnow()
            )

            db.session.add(action_log)
            db.session.delete(channel)
            db.session.commit()

            return success_response(
                data={"channel_id": channel_id},
                message="Sales channel deleted successfully"
            )
        else:
            return error_response(
                message="Sales channel management not configured",
                status_code=501,
                error_code="CHANNEL_MANAGEMENT_NOT_CONFIGURED"
            )

    except Exception as e:
        logger.error(f"Delete sales channel error: {e}")
        db.session.rollback()
        return error_response(
            message="Failed to delete sales channel",
            status_code=500,
            error_code="ADMIN_CHANNEL_DELETE_FAILED"
        )
