"""
Orders Routes
=============

Order and cart management endpoints with consistent response format.
"""

from flask import request
from datetime import datetime
import logging

from ..common.versioning import create_versioned_blueprint
from ..common.response_wrapper import (
    success_response, error_response, paginated_response, 
    not_found_response, validation_error_response
)
from ..common.auth_decorators import jwt_required_v2, rate_limit_v2, validate_content_type
from ..common.validators import (
    validate_json, validate_required_fields, validate_pagination,
    validate_quantity, validate_price
)

logger = logging.getLogger(__name__)

# Create versioned blueprint
orders_bp = create_versioned_blueprint('orders', __name__, url_prefix='/orders')

@orders_bp.route('', methods=['GET'])
@jwt_required_v2()
def get_orders(user):
    """
    Get user's orders with pagination.
    
    GET /api/v1/orders
    """
    try:
        page = request.args.get('page', 1, type=int)
        per_page = request.args.get('per_page', 20, type=int)
        status = request.args.get('status')
        
        # Validate pagination
        page, per_page = validate_pagination(page, per_page, max_per_page=50)
        
        from app import Order, OrderItem, Product
        
        # Build query
        query = Order.query.filter_by(user_id=user.id)
        
        if status:
            query = query.filter_by(status=status)
        
        query = query.order_by(Order.created_at.desc())
        
        # Execute paginated query
        paginated_orders = query.paginate(
            page=page,
            per_page=per_page,
            error_out=False
        )
        
        # Format orders data
        orders_data = []
        for order in paginated_orders.items:
            order_items = OrderItem.query.filter_by(order_id=order.id).all()
            
            items_data = []
            for item in order_items:
                product = Product.query.get(item.product_id)
                items_data.append({
                    "id": item.id,
                    "product_id": item.product_id,
                    "product_name": product.name if product else "Unknown Product",
                    "quantity": item.quantity,
                    "price": float(item.price),
                    "total": float(item.price * item.quantity)
                })
            
            order_data = {
                "id": order.id,
                "order_number": getattr(order, 'order_number', f"ORD-{order.id}"),
                "status": order.status,
                "total_amount": float(order.total_amount),
                "items_count": len(items_data),
                "items": items_data,
                "shipping_address": order.shipping_address,
                "created_at": order.created_at.isoformat() if order.created_at else None,
                "updated_at": order.updated_at.isoformat() if order.updated_at else None
            }
            orders_data.append(order_data)
        
        return paginated_response(
            data=orders_data,
            page=page,
            per_page=per_page,
            total=paginated_orders.total,
            message="Orders retrieved successfully"
        )
        
    except Exception as e:
        logger.error(f"Get orders error: {e}")
        return error_response(
            message="Failed to retrieve orders",
            status_code=500,
            error_code="ORDERS_FETCH_FAILED"
        )

@orders_bp.route('', methods=['POST'])
@jwt_required_v2()
@validate_content_type()
def create_order(user):
    """
    Create a new order.
    
    POST /api/v1/orders
    """
    try:
        # Validate JSON
        is_valid, data = validate_json()
        if not is_valid:
            return data
        
        # Validate required fields
        required_fields = ['items', 'shipping_address']
        is_valid, error_resp = validate_required_fields(data, required_fields)
        if not is_valid:
            return error_resp
        
        items = data['items']
        shipping_address = data['shipping_address']
        
        if not items or not isinstance(items, list):
            return validation_error_response(
                errors={"items": ["Items list is required and cannot be empty"]},
                message="Invalid items data"
            )
        
        # Validate items
        from app import db, Product, Order, OrderItem
        total_amount = 0
        validated_items = []
        
        for item in items:
            if not isinstance(item, dict):
                return validation_error_response(
                    errors={"items": ["Each item must be an object"]},
                    message="Invalid item format"
                )
            
            product_id = item.get('product_id')
            quantity = item.get('quantity')
            
            if not product_id or not quantity:
                return validation_error_response(
                    errors={"items": ["Each item must have product_id and quantity"]},
                    message="Missing item data"
                )
            
            # Validate quantity
            is_valid_qty, normalized_qty = validate_quantity(quantity)
            if not is_valid_qty or normalized_qty <= 0:
                return validation_error_response(
                    errors={"items": [f"Invalid quantity for product {product_id}"]},
                    message="Invalid quantity"
                )
            
            # Check product exists and has stock
            product = Product.query.get(product_id)
            if not product or not product.is_active:
                return validation_error_response(
                    errors={"items": [f"Product {product_id} not found or inactive"]},
                    message="Invalid product"
                )
            
            if product.stock_quantity < normalized_qty:
                return validation_error_response(
                    errors={"items": [f"Insufficient stock for product {product.name}"]},
                    message="Insufficient stock"
                )
            
            item_total = float(product.price) * normalized_qty
            total_amount += item_total
            
            validated_items.append({
                "product": product,
                "quantity": normalized_qty,
                "price": float(product.price),
                "total": item_total
            })
        
        # Create order
        new_order = Order(
            user_id=user.id,
            status='pending',
            total_amount=total_amount,
            shipping_address=shipping_address,
            created_at=datetime.utcnow()
        )
        
        db.session.add(new_order)
        db.session.flush()  # Get order ID
        
        # Create order items and update stock
        order_items_data = []
        for item in validated_items:
            order_item = OrderItem(
                order_id=new_order.id,
                product_id=item['product'].id,
                quantity=item['quantity'],
                price=item['price']
            )
            db.session.add(order_item)
            
            # Update product stock
            item['product'].stock_quantity -= item['quantity']
            
            order_items_data.append({
                "product_id": item['product'].id,
                "product_name": item['product'].name,
                "quantity": item['quantity'],
                "price": item['price'],
                "total": item['total']
            })
        
        db.session.commit()
        
        order_data = {
            "id": new_order.id,
            "order_number": f"ORD-{new_order.id}",
            "status": new_order.status,
            "total_amount": float(new_order.total_amount),
            "items": order_items_data,
            "shipping_address": new_order.shipping_address,
            "created_at": new_order.created_at.isoformat()
        }
        
        return success_response(
            data=order_data,
            message="Order created successfully",
            status_code=201
        )
        
    except Exception as e:
        logger.error(f"Create order error: {e}")
        db.session.rollback()
        return error_response(
            message="Failed to create order",
            status_code=500,
            error_code="ORDER_CREATION_FAILED"
        )

@orders_bp.route('/<int:order_id>', methods=['GET'])
@jwt_required_v2()
def get_order(user, order_id):
    """
    Get specific order details.
    
    GET /api/v1/orders/{order_id}
    """
    try:
        from app import Order, OrderItem, Product
        
        order = Order.query.filter_by(id=order_id, user_id=user.id).first()
        if not order:
            return not_found_response("Order", order_id)
        
        # Get order items
        order_items = OrderItem.query.filter_by(order_id=order.id).all()
        
        items_data = []
        for item in order_items:
            product = Product.query.get(item.product_id)
            items_data.append({
                "id": item.id,
                "product_id": item.product_id,
                "product_name": product.name if product else "Unknown Product",
                "product_image": product.images[0].image_url if product and product.images else None,
                "quantity": item.quantity,
                "price": float(item.price),
                "total": float(item.price * item.quantity)
            })
        
        order_data = {
            "id": order.id,
            "order_number": f"ORD-{order.id}",
            "status": order.status,
            "total_amount": float(order.total_amount),
            "items": items_data,
            "shipping_address": order.shipping_address,
            "created_at": order.created_at.isoformat() if order.created_at else None,
            "updated_at": order.updated_at.isoformat() if order.updated_at else None
        }
        
        return success_response(
            data=order_data,
            message="Order retrieved successfully"
        )
        
    except Exception as e:
        logger.error(f"Get order error: {e}")
        return error_response(
            message="Failed to retrieve order",
            status_code=500,
            error_code="ORDER_FETCH_FAILED"
        )

# Shopping Cart Endpoints

@orders_bp.route('/cart', methods=['GET'])
@jwt_required_v2()
@rate_limit_v2(limit=120, window=60, per='user')
def get_cart(user):
    """
    Get user's shopping cart.

    GET /api/v1/orders/cart
    """
    try:
        from app import db, Cart, CartItem, Product, ProductImage

        # Get or create cart for user
        cart = Cart.query.filter_by(user_id=user.id, status='active').first()

        if not cart:
            # Return empty cart
            return success_response(
                data={
                    "id": None,
                    "items": [],
                    "total_items": 0,
                    "subtotal": 0.0,
                    "tax": 0.0,
                    "shipping": 0.0,
                    "total": 0.0,
                    "currency": "INR",
                    "created_at": None,
                    "updated_at": None
                },
                message="Cart is empty"
            )

        # Get cart items with product details
        cart_items = db.session.query(CartItem, Product).join(
            Product, CartItem.product_id == Product.id
        ).filter(
            CartItem.cart_id == cart.id,
            Product.is_active == True
        ).all()

        items_data = []
        subtotal = 0.0

        for cart_item, product in cart_items:
            # Get product image
            product_image = ProductImage.query.filter_by(product_id=product.id).first()

            item_total = float(product.price * cart_item.quantity)
            subtotal += item_total

            items_data.append({
                "id": cart_item.id,
                "product_id": product.id,
                "product_name": product.name,
                "product_image": product_image.image_url if product_image else None,
                "unit_price": float(product.price),
                "quantity": cart_item.quantity,
                "total_price": item_total,
                "stock_available": product.stock_quantity,
                "in_stock": product.stock_quantity >= cart_item.quantity,
                "added_at": cart_item.created_at.isoformat() if cart_item.created_at else None
            })

        # Calculate totals (simplified - you might have complex tax/shipping logic)
        tax = subtotal * 0.18  # 18% GST
        shipping = 50.0 if subtotal < 500 else 0.0  # Free shipping above ₹500
        total = subtotal + tax + shipping

        cart_data = {
            "id": cart.id,
            "items": items_data,
            "total_items": len(items_data),
            "subtotal": round(subtotal, 2),
            "tax": round(tax, 2),
            "shipping": round(shipping, 2),
            "total": round(total, 2),
            "currency": "INR",
            "created_at": cart.created_at.isoformat() if cart.created_at else None,
            "updated_at": cart.updated_at.isoformat() if cart.updated_at else None
        }

        return success_response(
            data=cart_data,
            message="Cart retrieved successfully"
        )

    except Exception as e:
        logger.error(f"Get cart error: {e}")
        return error_response(
            message="Failed to retrieve cart",
            status_code=500,
            error_code="CART_FETCH_FAILED"
        )

@orders_bp.route('/cart', methods=['POST'])
@jwt_required_v2()
@validate_content_type()
@rate_limit_v2(limit=60, window=60, per='user')
def add_to_cart(user):
    """
    Add item to shopping cart.

    POST /api/v1/orders/cart
    """
    try:
        # Validate JSON
        is_valid, data = validate_json()
        if not is_valid:
            return data

        # Validate required fields
        required_fields = ['product_id', 'quantity']
        is_valid, error_resp = validate_required_fields(data, required_fields)
        if not is_valid:
            return error_resp

        product_id = data['product_id']
        quantity = data['quantity']

        # Validate quantity
        is_valid_qty, normalized_qty = validate_quantity(quantity)
        if not is_valid_qty:
            return validation_error_response(
                errors={"quantity": ["Invalid quantity"]},
                message="Invalid quantity"
            )

        from app import db, Product, Cart, CartItem

        # Check if product exists and is active
        product = Product.query.filter_by(id=product_id, is_active=True).first()
        if not product:
            return not_found_response("Product", product_id)

        # Check stock availability
        if product.stock_quantity < normalized_qty:
            return error_response(
                message=f"Only {product.stock_quantity} items available in stock",
                status_code=400,
                error_code="INSUFFICIENT_STOCK"
            )

        # Get or create cart for user
        cart = Cart.query.filter_by(user_id=user.id, status='active').first()
        if not cart:
            cart = Cart(
                user_id=user.id,
                status='active',
                created_at=datetime.utcnow()
            )
            db.session.add(cart)
            db.session.flush()  # Get cart ID

        # Check if item already exists in cart
        existing_item = CartItem.query.filter_by(
            cart_id=cart.id,
            product_id=product_id
        ).first()

        if existing_item:
            # Update quantity
            new_quantity = existing_item.quantity + normalized_qty

            # Check stock for new quantity
            if product.stock_quantity < new_quantity:
                return error_response(
                    message=f"Cannot add {normalized_qty} more items. Only {product.stock_quantity - existing_item.quantity} more available",
                    status_code=400,
                    error_code="INSUFFICIENT_STOCK"
                )

            existing_item.quantity = new_quantity
            existing_item.updated_at = datetime.utcnow()
            cart_item = existing_item
        else:
            # Create new cart item
            cart_item = CartItem(
                cart_id=cart.id,
                product_id=product_id,
                quantity=normalized_qty,
                created_at=datetime.utcnow()
            )
            db.session.add(cart_item)

        # Update cart timestamp
        cart.updated_at = datetime.utcnow()

        db.session.commit()

        # Return updated cart item
        item_data = {
            "id": cart_item.id,
            "product_id": product.id,
            "product_name": product.name,
            "unit_price": float(product.price),
            "quantity": cart_item.quantity,
            "total_price": float(product.price * cart_item.quantity),
            "added_at": cart_item.created_at.isoformat()
        }

        return success_response(
            data=item_data,
            message="Item added to cart successfully",
            status_code=201
        )

    except Exception as e:
        logger.error(f"Add to cart error: {e}")
        db.session.rollback()
        return error_response(
            message="Failed to add item to cart",
            status_code=500,
            error_code="CART_ADD_FAILED"
        )

@orders_bp.route('/cart', methods=['PUT'])
@jwt_required_v2()
@validate_content_type()
@rate_limit_v2(limit=60, window=60, per='user')
def update_cart_item(user):
    """
    Update cart item quantity.

    PUT /api/v1/orders/cart
    """
    try:
        # Validate JSON
        is_valid, data = validate_json()
        if not is_valid:
            return data

        # Validate required fields
        required_fields = ['cart_item_id', 'quantity']
        is_valid, error_resp = validate_required_fields(data, required_fields)
        if not is_valid:
            return error_resp

        cart_item_id = data['cart_item_id']
        quantity = data['quantity']

        # Validate quantity
        is_valid_qty, normalized_qty = validate_quantity(quantity)
        if not is_valid_qty:
            return validation_error_response(
                errors={"quantity": ["Invalid quantity"]},
                message="Invalid quantity"
            )

        from app import db, CartItem, Product, Cart

        # Get cart item
        cart_item = CartItem.query.join(Cart).filter(
            CartItem.id == cart_item_id,
            Cart.user_id == user.id,
            Cart.status == 'active'
        ).first()

        if not cart_item:
            return not_found_response("Cart item", cart_item_id)

        # Get product to check stock
        product = Product.query.get(cart_item.product_id)
        if not product or not product.is_active:
            return error_response(
                message="Product is no longer available",
                status_code=400,
                error_code="PRODUCT_UNAVAILABLE"
            )

        # Check stock availability
        if product.stock_quantity < normalized_qty:
            return error_response(
                message=f"Only {product.stock_quantity} items available in stock",
                status_code=400,
                error_code="INSUFFICIENT_STOCK"
            )

        # Update quantity
        cart_item.quantity = normalized_qty
        cart_item.updated_at = datetime.utcnow()

        # Update cart timestamp
        cart = Cart.query.get(cart_item.cart_id)
        cart.updated_at = datetime.utcnow()

        db.session.commit()

        # Return updated cart item
        item_data = {
            "id": cart_item.id,
            "product_id": product.id,
            "product_name": product.name,
            "unit_price": float(product.price),
            "quantity": cart_item.quantity,
            "total_price": float(product.price * cart_item.quantity),
            "updated_at": cart_item.updated_at.isoformat()
        }

        return success_response(
            data=item_data,
            message="Cart item updated successfully"
        )

    except Exception as e:
        logger.error(f"Update cart item error: {e}")
        db.session.rollback()
        return error_response(
            message="Failed to update cart item",
            status_code=500,
            error_code="CART_UPDATE_FAILED"
        )

@orders_bp.route('/cart/<int:cart_item_id>', methods=['DELETE'])
@jwt_required_v2()
@rate_limit_v2(limit=60, window=60, per='user')
def remove_from_cart(user, cart_item_id):
    """
    Remove item from shopping cart.

    DELETE /api/v1/orders/cart/{cart_item_id}
    """
    try:
        from app import db, CartItem, Cart

        # Get cart item
        cart_item = CartItem.query.join(Cart).filter(
            CartItem.id == cart_item_id,
            Cart.user_id == user.id,
            Cart.status == 'active'
        ).first()

        if not cart_item:
            return not_found_response("Cart item", cart_item_id)

        # Update cart timestamp before removing item
        cart = Cart.query.get(cart_item.cart_id)
        cart.updated_at = datetime.utcnow()

        # Remove cart item
        db.session.delete(cart_item)
        db.session.commit()

        return success_response(
            data={"cart_item_id": cart_item_id},
            message="Item removed from cart successfully"
        )

    except Exception as e:
        logger.error(f"Remove from cart error: {e}")
        db.session.rollback()
        return error_response(
            message="Failed to remove item from cart",
            status_code=500,
            error_code="CART_REMOVE_FAILED"
        )

@orders_bp.route('/cart/clear', methods=['DELETE'])
@jwt_required_v2()
@rate_limit_v2(limit=10, window=60, per='user')
def clear_cart(user):
    """
    Clear all items from shopping cart.

    DELETE /api/v1/orders/cart/clear
    """
    try:
        from app import db, Cart, CartItem

        # Get user's active cart
        cart = Cart.query.filter_by(user_id=user.id, status='active').first()

        if not cart:
            return success_response(
                data={"cleared_items": 0},
                message="Cart is already empty"
            )

        # Count items before clearing
        items_count = CartItem.query.filter_by(cart_id=cart.id).count()

        # Remove all cart items
        CartItem.query.filter_by(cart_id=cart.id).delete()

        # Update cart status or delete cart
        cart.status = 'cleared'
        cart.updated_at = datetime.utcnow()

        db.session.commit()

        return success_response(
            data={"cleared_items": items_count},
            message="Cart cleared successfully"
        )

    except Exception as e:
        logger.error(f"Clear cart error: {e}")
        db.session.rollback()
        return error_response(
            message="Failed to clear cart",
            status_code=500,
            error_code="CART_CLEAR_FAILED"
        )

# Checkout and Payment Endpoints

@orders_bp.route('/checkout', methods=['POST'])
@jwt_required_v2()
@validate_content_type()
@rate_limit_v2(limit=10, window=300, per='user')  # 10 checkouts per 5 minutes
def checkout(user):
    """
    Process checkout and create order.

    POST /api/v1/orders/checkout
    """
    try:
        # Validate JSON
        is_valid, data = validate_json()
        if not is_valid:
            return data

        # Validate required fields
        required_fields = ['shipping_address_id', 'payment_method']
        is_valid, error_resp = validate_required_fields(data, required_fields)
        if not is_valid:
            return error_resp

        shipping_address_id = data['shipping_address_id']
        payment_method = data['payment_method']
        payment_details = data.get('payment_details', {})

        from app import db, Cart, CartItem, Product, Order, OrderItem, Address

        # Get user's active cart
        cart = Cart.query.filter_by(user_id=user.id, status='active').first()
        if not cart:
            return error_response(
                message="Cart is empty",
                status_code=400,
                error_code="EMPTY_CART"
            )

        # Get cart items
        cart_items = CartItem.query.filter_by(cart_id=cart.id).all()
        if not cart_items:
            return error_response(
                message="Cart is empty",
                status_code=400,
                error_code="EMPTY_CART"
            )

        # Validate shipping address
        shipping_address = Address.query.filter_by(
            id=shipping_address_id,
            user_id=user.id
        ).first()
        if not shipping_address:
            return not_found_response("Shipping address", shipping_address_id)

        # Validate stock availability and calculate totals
        order_items_data = []
        subtotal = 0.0

        for cart_item in cart_items:
            product = Product.query.get(cart_item.product_id)
            if not product or not product.is_active:
                return error_response(
                    message=f"Product {cart_item.product_id} is no longer available",
                    status_code=400,
                    error_code="PRODUCT_UNAVAILABLE"
                )

            if product.stock_quantity < cart_item.quantity:
                return error_response(
                    message=f"Insufficient stock for {product.name}. Only {product.stock_quantity} available",
                    status_code=400,
                    error_code="INSUFFICIENT_STOCK"
                )

            item_total = float(product.price * cart_item.quantity)
            subtotal += item_total

            order_items_data.append({
                'product': product,
                'quantity': cart_item.quantity,
                'price': product.price,
                'total': item_total
            })

        # Calculate totals
        tax = subtotal * 0.18  # 18% GST
        shipping = 50.0 if subtotal < 500 else 0.0
        total_amount = subtotal + tax + shipping

        # Create order
        new_order = Order(
            user_id=user.id,
            shipping_address_id=shipping_address_id,
            payment_method=payment_method,
            subtotal=subtotal,
            tax_amount=tax,
            shipping_amount=shipping,
            total_amount=total_amount,
            status='pending',
            payment_status='pending',
            created_at=datetime.utcnow()
        )

        db.session.add(new_order)
        db.session.flush()  # Get order ID

        # Create order items and update stock
        for item_data in order_items_data:
            product = item_data['product']
            quantity = item_data['quantity']

            # Create order item
            order_item = OrderItem(
                order_id=new_order.id,
                product_id=product.id,
                quantity=quantity,
                price=product.price,
                created_at=datetime.utcnow()
            )
            db.session.add(order_item)

            # Update product stock
            product.stock_quantity -= quantity

        # Clear cart
        CartItem.query.filter_by(cart_id=cart.id).delete()
        cart.status = 'checked_out'
        cart.updated_at = datetime.utcnow()

        db.session.commit()

        # Format order data
        order_data = {
            "id": new_order.id,
            "order_number": f"ORD-{new_order.id:06d}",
            "status": new_order.status,
            "payment_status": new_order.payment_status,
            "subtotal": float(new_order.subtotal),
            "tax": float(new_order.tax_amount),
            "shipping": float(new_order.shipping_amount),
            "total": float(new_order.total_amount),
            "currency": "INR",
            "created_at": new_order.created_at.isoformat()
        }

        return success_response(
            data=order_data,
            message="Order created successfully",
            status_code=201
        )

    except Exception as e:
        logger.error(f"Checkout error: {e}")
        db.session.rollback()
        return error_response(
            message="Failed to process checkout",
            status_code=500,
            error_code="CHECKOUT_FAILED"
        )

@orders_bp.route('/checkout/guest', methods=['POST'])
@validate_content_type()
@rate_limit_v2(limit=5, window=300, per='ip')  # 5 guest checkouts per 5 minutes
def guest_checkout():
    """
    Process guest checkout without user account.

    POST /api/v1/orders/checkout/guest
    """
    try:
        # Validate JSON
        is_valid, data = validate_json()
        if not is_valid:
            return data

        # Validate required fields
        required_fields = ['email', 'shipping_address', 'items', 'payment_method']
        is_valid, error_resp = validate_required_fields(data, required_fields)
        if not is_valid:
            return error_resp

        email = data['email'].strip().lower()
        shipping_address = data['shipping_address']
        items = data['items']
        payment_method = data['payment_method']

        # Validate email
        from ..common.validators import validate_email
        if not validate_email(email):
            return validation_error_response(
                errors={"email": ["Invalid email format"]},
                message="Invalid email"
            )

        # Validate items
        if not isinstance(items, list) or len(items) == 0:
            return validation_error_response(
                errors={"items": ["Items list cannot be empty"]},
                message="No items provided"
            )

        from app import db, Product, Order, OrderItem

        # Validate products and calculate totals
        order_items_data = []
        subtotal = 0.0

        for item in items:
            if not isinstance(item, dict) or 'product_id' not in item or 'quantity' not in item:
                return validation_error_response(
                    errors={"items": ["Each item must have product_id and quantity"]},
                    message="Invalid item format"
                )

            product_id = item['product_id']
            quantity = item['quantity']

            # Validate quantity
            is_valid_qty, normalized_qty = validate_quantity(quantity)
            if not is_valid_qty:
                return validation_error_response(
                    errors={"items": [f"Invalid quantity for product {product_id}"]},
                    message="Invalid quantity"
                )

            # Check product
            product = Product.query.filter_by(id=product_id, is_active=True).first()
            if not product:
                return error_response(
                    message=f"Product {product_id} not found",
                    status_code=404,
                    error_code="PRODUCT_NOT_FOUND"
                )

            if product.stock_quantity < normalized_qty:
                return error_response(
                    message=f"Insufficient stock for {product.name}",
                    status_code=400,
                    error_code="INSUFFICIENT_STOCK"
                )

            item_total = float(product.price * normalized_qty)
            subtotal += item_total

            order_items_data.append({
                'product': product,
                'quantity': normalized_qty,
                'price': product.price,
                'total': item_total
            })

        # Calculate totals
        tax = subtotal * 0.18
        shipping = 50.0 if subtotal < 500 else 0.0
        total_amount = subtotal + tax + shipping

        # Create guest order
        new_order = Order(
            user_id=None,  # Guest order
            guest_email=email,
            guest_shipping_address=shipping_address,
            payment_method=payment_method,
            subtotal=subtotal,
            tax_amount=tax,
            shipping_amount=shipping,
            total_amount=total_amount,
            status='pending',
            payment_status='pending',
            created_at=datetime.utcnow()
        )

        db.session.add(new_order)
        db.session.flush()

        # Create order items and update stock
        for item_data in order_items_data:
            product = item_data['product']
            quantity = item_data['quantity']

            order_item = OrderItem(
                order_id=new_order.id,
                product_id=product.id,
                quantity=quantity,
                price=product.price,
                created_at=datetime.utcnow()
            )
            db.session.add(order_item)

            # Update stock
            product.stock_quantity -= quantity

        db.session.commit()

        order_data = {
            "id": new_order.id,
            "order_number": f"ORD-{new_order.id:06d}",
            "status": new_order.status,
            "payment_status": new_order.payment_status,
            "total": float(new_order.total_amount),
            "currency": "INR",
            "guest_email": email,
            "created_at": new_order.created_at.isoformat()
        }

        return success_response(
            data=order_data,
            message="Guest order created successfully",
            status_code=201
        )

    except Exception as e:
        logger.error(f"Guest checkout error: {e}")
        db.session.rollback()
        return error_response(
            message="Failed to process guest checkout",
            status_code=500,
            error_code="GUEST_CHECKOUT_FAILED"
        )

# Additional Cart Features (migrated from app.py)

@orders_bp.route('/cart/smart-bundles', methods=['GET'])
@jwt_required_v2()
@rate_limit_v2(limit=60, window=60, per='user')
def get_smart_bundles(user):
    """Get smart bundle recommendations based on current cart contents (migrated from app.py)"""
    try:
        from app import CartItem, Product

        # Get current cart items
        cart_items = CartItem.query.filter_by(user_id=user.id).all()
        if not cart_items:
            return success_response(
                data={'bundles': []},
                message='Add items to cart to see smart bundles'
            )

        # Get product IDs in cart
        cart_product_ids = [item.product_id for item in cart_items]

        # Define smart bundling rules based on actual product catalog
        bundling_rules = [
            {
                'name': 'Fashion Essentials',
                'description': 'Complete your sustainable wardrobe',
                'triggers': ['t-shirt', 'shirt', 'jeans', 'cotton'],
                'suggestions': ['backpack', 'sneakers', 'scarf', 'wallet', 'sunglasses'],
                'category': 'fashion'
            },
            {
                'name': 'Eco Accessories',
                'description': 'Perfect accessories for your sustainable lifestyle',
                'triggers': ['backpack', 'bag', 'wallet', 'sunglasses'],
                'suggestions': ['scarf', 'bottle', 'notebook', 'straw'],
                'category': 'accessories'
            },
            {
                'name': 'Kitchen Essentials',
                'description': 'Complete your eco-friendly kitchen setup',
                'triggers': ['cutting board', 'kitchen', 'lunch box', 'mug', 'straw'],
                'suggestions': ['bottle', 'toothbrush', 'planter', 'notebook'],
                'category': 'kitchen'
            },
            {
                'name': 'Wellness Bundle',
                'description': 'Everything for your healthy, sustainable routine',
                'triggers': ['toothbrush', 'bottle', 'yoga', 'mat'],
                'suggestions': ['straw', 'mug', 'lunch box', 'cutting board'],
                'category': 'wellness'
            }
        ]

        # Get cart products with details
        cart_products = Product.query.filter(Product.id.in_(cart_product_ids)).all()

        # Analyze cart contents to find applicable bundles
        applicable_bundles = []

        for rule in bundling_rules:
            # Check if any cart items match the triggers
            cart_matches_trigger = False
            for product in cart_products:
                product_text = f"{product.name} {product.description or ''} {product.category or ''}".lower()
                if any(trigger.lower() in product_text for trigger in rule['triggers']):
                    cart_matches_trigger = True
                    break

            if cart_matches_trigger:
                # Find suggested products that aren't already in cart
                suggested_products = []
                for suggestion in rule['suggestions']:
                    # Find products matching the suggestion
                    matching_products = Product.query.filter(
                        ~Product.id.in_(cart_product_ids),  # Not already in cart
                        Product.is_active == True,
                        db.or_(
                            Product.name.ilike(f'%{suggestion}%'),
                            Product.description.ilike(f'%{suggestion}%'),
                            Product.category.ilike(f'%{suggestion}%')
                        )
                    ).limit(3).all()

                    for product in matching_products:
                        suggested_products.append({
                            'id': product.id,
                            'name': product.name,
                            'price': float(product.price),
                            'image': product.image,
                            'category': product.category,
                            'discount_percentage': 10  # Bundle discount
                        })

                if suggested_products:
                    # Calculate bundle savings
                    original_total = sum(p['price'] for p in suggested_products)
                    bundle_total = original_total * 0.9  # 10% discount
                    savings = original_total - bundle_total

                    applicable_bundles.append({
                        'id': f"bundle_{rule['category']}",
                        'name': rule['name'],
                        'description': rule['description'],
                        'category': rule['category'],
                        'products': suggested_products[:4],  # Limit to 4 suggestions
                        'original_total': round(original_total, 2),
                        'bundle_total': round(bundle_total, 2),
                        'savings': round(savings, 2),
                        'discount_percentage': 10
                    })

        return success_response(data={'bundles': applicable_bundles})

    except Exception as e:
        logger.error(f"Get smart bundles error: {str(e)}")
        return error_response(
            message="Failed to load smart bundles",
            status_code=500,
            error_code="CART_SMART_BUNDLES_FETCH_FAILED"
        )

@orders_bp.route('/cart/add-bundle', methods=['POST'])
@jwt_required_v2()
@validate_content_type()
@rate_limit_v2(limit=30, window=300, per='user')
def add_bundle_to_cart(user):
    """Add all products from a smart bundle to cart (migrated from app.py)"""
    try:
        # Validate JSON
        is_valid, data = validate_json()
        if not is_valid:
            return data

        product_ids = data.get('product_ids', [])

        if not product_ids:
            return validation_error_response(
                errors={"product_ids": ["No products specified"]},
                message="No products specified"
            )

        from app import db, Product, CartItem

        added_items = []

        for product_id in product_ids:
            # Check if product exists and has stock
            product = Product.query.get(product_id)
            if not product:
                continue

            if product.stock_quantity < 1:
                continue

            # Check if item already exists in cart
            cart_item = CartItem.query.filter_by(user_id=user.id, product_id=product_id).first()

            if cart_item:
                # Increase quantity if already in cart
                cart_item.quantity += 1
            else:
                # Add new item to cart
                cart_item = CartItem(user_id=user.id, product_id=product_id, quantity=1)
                db.session.add(cart_item)

            added_items.append({
                'id': cart_item.id if cart_item.id else 'new',
                'product_id': product.id,
                'product_name': product.name,
                'price': float(product.price) if product.price else 0.0,
                'quantity': cart_item.quantity,
                'image': product.image
            })

        db.session.commit()

        return success_response(
            data={'added_items': added_items},
            message=f'Added {len(added_items)} items to cart',
            status_code=201
        )

    except Exception as e:
        db.session.rollback()
        logger.error(f"Add bundle to cart error: {str(e)}")
        return error_response(
            message="Failed to add bundle to cart",
            status_code=500,
            error_code="CART_ADD_BUNDLE_FAILED"
        )

# Saved Cart Management (migrated from app.py)

@orders_bp.route('/cart/save', methods=['POST'])
@validate_content_type()
@rate_limit_v2(limit=10, window=300, per='user')
def save_cart_for_later():
    """Save current cart for later (migrated from app.py)"""
    try:
        # Validate JSON
        is_valid, data = validate_json()
        if not is_valid:
            return data

        cart_name = data.get('name', 'Saved Cart')
        guest_session_id = data.get('guest_session_id')

        from app import db, CartItem, Product, SavedCart

        # Try to get authenticated user
        user = None
        user_id = None
        try:
            from allora.backend.blueprints.auth.auth_utils import verify_token_v2
            user = verify_token_v2()
            user_id = user.id if user else None
        except:
            pass

        # Get current cart items with joins to avoid N+1 queries
        if user_id:
            cart_items = db.session.query(CartItem).join(Product).filter(
                CartItem.user_id == user_id
            ).all()
        elif guest_session_id:
            cart_items = db.session.query(CartItem).join(Product).filter(
                CartItem.guest_session_id == guest_session_id
            ).all()
        else:
            return validation_error_response(
                errors={"cart": ["No cart found"]},
                message="No cart found"
            )

        if not cart_items:
            return validation_error_response(
                errors={"cart": ["Cart is empty"]},
                message="Cart is empty"
            )

        # Convert cart items to JSON
        cart_data = []
        for item in cart_items:
            cart_data.append({
                'product_id': item.product.id,
                'product_name': item.product.name,
                'price': float(item.product.price) if item.product.price else 0.0,
                'quantity': item.quantity
            })

        # Save cart
        saved_cart = SavedCart(
            user_id=user_id,
            guest_session_id=guest_session_id,
            name=cart_name,
            cart_data=cart_data
        )

        db.session.add(saved_cart)
        db.session.commit()

        return success_response(
            data={'saved_cart_id': saved_cart.id},
            message='Cart saved successfully',
            status_code=201
        )

    except Exception as e:
        db.session.rollback()
        logger.error(f"Save cart error: {str(e)}")
        return error_response(
            message="Failed to save cart",
            status_code=500,
            error_code="CART_SAVE_FAILED"
        )

@orders_bp.route('/cart/saved', methods=['GET'])
@jwt_required_v2()
@rate_limit_v2(limit=60, window=60, per='user')
def get_saved_carts(user):
    """Get user's saved carts (migrated from app.py)"""
    try:
        from app import SavedCart

        saved_carts = SavedCart.query.filter_by(user_id=user.id).order_by(SavedCart.created_at.desc()).all()

        carts_data = []
        for cart in saved_carts:
            carts_data.append({
                'id': cart.id,
                'name': cart.name,
                'cart_data': cart.cart_data,
                'created_at': cart.created_at.isoformat()
            })

        return success_response(data=carts_data)

    except Exception as e:
        logger.error(f"Get saved carts error: {str(e)}")
        return error_response(
            message="Failed to fetch saved carts",
            status_code=500,
            error_code="SAVED_CARTS_FETCH_FAILED"
        )

@orders_bp.route('/cart/restore/<int:saved_cart_id>', methods=['POST'])
@jwt_required_v2()
@rate_limit_v2(limit=10, window=300, per='user')
def restore_saved_cart(user, saved_cart_id):
    """Restore a saved cart to current cart (migrated from app.py)"""
    try:
        from app import db, SavedCart, CartItem, Product

        saved_cart = SavedCart.query.filter_by(id=saved_cart_id, user_id=user.id).first()

        if not saved_cart:
            return not_found_response("Saved cart", saved_cart_id)

        # Clear current cart
        CartItem.query.filter_by(user_id=user.id).delete()

        # Restore items from saved cart
        restored_items = []
        for item_data in saved_cart.cart_data:
            # Check if product still exists and has stock
            product = Product.query.get(item_data['product_id'])
            if product and product.stock_quantity >= item_data['quantity']:
                cart_item = CartItem(
                    user_id=user.id,
                    product_id=item_data['product_id'],
                    quantity=item_data['quantity']
                )
                db.session.add(cart_item)
                restored_items.append({
                    'product_id': item_data['product_id'],
                    'product_name': item_data['product_name'],
                    'quantity': item_data['quantity']
                })

        db.session.commit()

        return success_response(
            data={'restored_items': restored_items},
            message='Cart restored successfully'
        )

    except Exception as e:
        db.session.rollback()
        logger.error(f"Restore cart error: {str(e)}")
        return error_response(
            message="Failed to restore cart",
            status_code=500,
            error_code="CART_RESTORE_FAILED"
        )

@orders_bp.route('/cart/saved/<int:saved_cart_id>', methods=['DELETE'])
@jwt_required_v2()
@rate_limit_v2(limit=30, window=300, per='user')
def delete_saved_cart(user, saved_cart_id):
    """Delete a saved cart (migrated from app.py)"""
    try:
        from app import db, SavedCart

        saved_cart = SavedCart.query.filter_by(id=saved_cart_id, user_id=user.id).first()

        if not saved_cart:
            return not_found_response("Saved cart", saved_cart_id)

        db.session.delete(saved_cart)
        db.session.commit()

        return success_response(message='Saved cart deleted successfully')

    except Exception as e:
        db.session.rollback()
        logger.error(f"Delete saved cart error: {str(e)}")
        return error_response(
            message="Failed to delete saved cart",
            status_code=500,
            error_code="SAVED_CART_DELETE_FAILED"
        )

# Abandoned Cart Management (migrated from app.py)

@orders_bp.route('/cart/abandon', methods=['POST'])
@validate_content_type()
@rate_limit_v2(limit=10, window=300, per='ip')
def track_abandoned_cart():
    """Track abandoned cart for recovery (migrated from app.py)"""
    try:
        # Validate JSON
        is_valid, data = validate_json()
        if not is_valid:
            return data

        email = data.get('email')
        cart_data = data.get('cart_data', [])
        cart_total = data.get('cart_total', 0)
        user_id = data.get('user_id')
        guest_session_id = data.get('guest_session_id')

        if not email or not cart_data:
            return validation_error_response(
                errors={"email": ["Email required"], "cart_data": ["Cart data required"]},
                message="Email and cart data are required"
            )

        from app import db, AbandonedCart
        import uuid

        # Generate recovery token
        recovery_token = str(uuid.uuid4())

        # Check if abandoned cart already exists for this user/session
        existing_cart = None
        if user_id:
            existing_cart = AbandonedCart.query.filter_by(user_id=user_id, is_recovered=False).first()
        elif guest_session_id:
            existing_cart = AbandonedCart.query.filter_by(guest_session_id=guest_session_id, is_recovered=False).first()

        if existing_cart:
            # Update existing abandoned cart
            existing_cart.cart_data = cart_data
            existing_cart.cart_total = cart_total
            existing_cart.recovery_token = recovery_token
            existing_cart.created_at = datetime.utcnow()
            abandoned_cart = existing_cart
        else:
            # Create new abandoned cart record
            abandoned_cart = AbandonedCart(
                user_id=user_id,
                guest_session_id=guest_session_id,
                email=email,
                cart_data=cart_data,
                cart_total=cart_total,
                recovery_token=recovery_token
            )
            db.session.add(abandoned_cart)

        db.session.commit()

        return success_response(
            data={'recovery_token': recovery_token},
            message='Abandoned cart tracked successfully',
            status_code=201
        )

    except Exception as e:
        db.session.rollback()
        logger.error(f"Track abandoned cart error: {str(e)}")
        return error_response(
            message="Failed to track abandoned cart",
            status_code=500,
            error_code="ABANDONED_CART_TRACK_FAILED"
        )

@orders_bp.route('/cart/recover/<recovery_token>', methods=['GET'])
@rate_limit_v2(limit=30, window=300, per='ip')
def recover_abandoned_cart(recovery_token):
    """Recover abandoned cart using recovery token (migrated from app.py)"""
    try:
        from app import db, AbandonedCart

        abandoned_cart = AbandonedCart.query.filter_by(recovery_token=recovery_token, is_recovered=False).first()

        if not abandoned_cart:
            return not_found_response("Recovery link", "invalid_or_expired")

        # Mark as recovered
        abandoned_cart.is_recovered = True
        abandoned_cart.recovered_at = datetime.utcnow()
        db.session.commit()

        cart_data = {
            'cart_data': abandoned_cart.cart_data,
            'cart_total': abandoned_cart.cart_total,
            'email': abandoned_cart.email
        }

        return success_response(
            data=cart_data,
            message='Cart recovered successfully'
        )

    except Exception as e:
        logger.error(f"Recover abandoned cart error: {str(e)}")
        return error_response(
            message="Failed to recover cart",
            status_code=500,
            error_code="ABANDONED_CART_RECOVER_FAILED"
        )

@orders_bp.route('/cart/abandon/send-reminder', methods=['POST'])
@rate_limit_v2(limit=5, window=3600, per='ip')
def send_abandonment_reminder():
    """Send cart abandonment reminder emails (migrated from app.py)"""
    try:
        from app import db, AbandonedCart
        from datetime import timedelta

        # Find abandoned carts older than 1 hour with less than 3 reminders
        cutoff_time = datetime.utcnow() - timedelta(hours=1)
        abandoned_carts = AbandonedCart.query.filter(
            AbandonedCart.created_at <= cutoff_time,
            AbandonedCart.is_recovered == False,
            AbandonedCart.reminder_count < 3
        ).all()

        sent_count = 0
        for cart in abandoned_carts:
            # Check if enough time has passed since last reminder (24 hours)
            if cart.last_reminder_sent:
                time_since_last = datetime.utcnow() - cart.last_reminder_sent
                if time_since_last.total_seconds() < 24 * 3600:  # 24 hours
                    continue

            # Send reminder email (placeholder - integrate with email service)
            recovery_link = f"http://localhost:3000/cart-recovery/{cart.recovery_token}"

            # Update reminder tracking
            cart.last_reminder_sent = datetime.utcnow()
            cart.reminder_count += 1
            sent_count += 1

        db.session.commit()

        return success_response(
            data={'sent_count': sent_count},
            message=f'Sent {sent_count} cart abandonment reminders'
        )

    except Exception as e:
        db.session.rollback()
        logger.error(f"Send abandonment reminder error: {str(e)}")
        return error_response(
            message="Failed to send reminders",
            status_code=500,
            error_code="ABANDONMENT_REMINDER_FAILED"
        )
