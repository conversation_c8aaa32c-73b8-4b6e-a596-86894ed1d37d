"""
Products Routes
===============

Product-related endpoints with consistent response format and URL patterns.
"""

from flask import request
from datetime import datetime
import logging

from ..common.versioning import create_versioned_blueprint
from ..common.response_wrapper import (
    success_response, error_response, paginated_response, not_found_response
)
from ..common.auth_decorators import jwt_required_v2, optional_auth, rate_limit_v2, validate_content_type
from ..common.validators import (
    validate_pagination, validate_sort_params, validate_price,
    validate_json, validate_required_fields, validation_error_response
)

logger = logging.getLogger(__name__)

# Create versioned blueprint
products_bp = create_versioned_blueprint('products', __name__, url_prefix='/products')

@products_bp.route('', methods=['GET'])
@rate_limit_v2(limit=120, window=60, per='ip')  # 120 requests per minute
def get_products():
    """
    Get products with advanced filtering, search, and pagination.

    GET /api/v1/products
    Query Parameters:
    - page: Page number (default: 1)
    - per_page: Items per page (default: 20, max: 50)
    - category: Filter by category
    - brand: Filter by brand
    - min_price: Minimum price filter
    - max_price: Maximum price filter
    - min_rating: Minimum rating filter
    - search: Search query (uses Elasticsearch if available)
    - q: Alternative search parameter
    - sort_by: Sort field (relevance, name, price, rating, created_at)
    - sort_order: Sort order (asc, desc)
    - in_stock_only: Filter to in-stock products only (true/false)
    - verified_sellers_only: Filter to verified sellers only (true/false)
    - seller_name: Filter by seller name
    """
    try:
        import time
        start_time = time.time()

        # Try to use Elasticsearch for advanced search if available
        try:
            from search_system.elasticsearch_search import get_search_engine

            # Check if this is a search request that would benefit from Elasticsearch
            search_query = request.args.get('search', '').strip()
            if search_query or request.args.get('q', '').strip():
                # Use Elasticsearch for text search
                search_engine = get_search_engine()

                # Map request parameters to Elasticsearch format
                filters = {}

                # Category filters
                if request.args.get('category'):
                    filters['categories'] = [cat.strip() for cat in request.args.get('category').split(',')]

                # Brand filters
                if request.args.get('brand'):
                    filters['brands'] = [brand.strip() for brand in request.args.get('brand').split(',')]

                # Price range filters
                if request.args.get('min_price'):
                    try:
                        filters['price_min'] = float(request.args.get('min_price'))
                    except ValueError:
                        pass

                if request.args.get('max_price'):
                    try:
                        filters['price_max'] = float(request.args.get('max_price'))
                    except ValueError:
                        pass

                # Rating filter
                if request.args.get('min_rating'):
                    try:
                        filters['min_rating'] = float(request.args.get('min_rating'))
                    except ValueError:
                        pass

                # Boolean filters
                if request.args.get('in_stock_only', '').lower() == 'true':
                    filters['in_stock_only'] = True

                if request.args.get('verified_sellers_only', '').lower() == 'true':
                    filters['verified_sellers_only'] = True

                # Seller filters
                if request.args.get('seller_name'):
                    filters['sellers'] = [request.args.get('seller_name').strip()]

                # Sorting parameters
                sort_by = request.args.get('sort_by', 'relevance')
                sort_order = request.args.get('sort_order', 'desc')

                # Pagination parameters
                try:
                    page = max(1, int(request.args.get('page', 1)))
                except ValueError:
                    page = 1

                try:
                    per_page = min(50, max(1, int(request.args.get('per_page', 20))))
                except ValueError:
                    per_page = 20

                # Execute Elasticsearch search
                query_text = search_query or request.args.get('q', '').strip()
                results = search_engine.search_products(
                    query=query_text,
                    filters=filters,
                    sort_by=sort_by,
                    sort_order=sort_order,
                    page=page,
                    per_page=per_page,
                    include_facets=True
                )

                # Format Elasticsearch results
                products_data = []
                for hit in results.get('hits', []):
                    source = hit.get('_source', {})
                    products_data.append({
                        "id": source.get('id'),
                        "name": source.get('name'),
                        "description": source.get('description'),
                        "price": float(source.get('price', 0)),
                        "category": source.get('category'),
                        "brand": source.get('brand'),
                        "stock_quantity": source.get('stock_quantity', 0),
                        "rating": source.get('average_rating'),
                        "image_url": source.get('image_url'),
                        "seller_name": source.get('seller_name'),
                        "created_at": source.get('created_at'),
                        "relevance_score": hit.get('_score')
                    })

                # Calculate execution time
                execution_time = time.time() - start_time

                return paginated_response(
                    data=products_data,
                    page=page,
                    per_page=per_page,
                    total=results.get('total', 0),
                    message="Products retrieved successfully via Elasticsearch",
                    meta={
                        "search_engine": "elasticsearch",
                        "execution_time": round(execution_time, 3),
                        "facets": results.get('facets', {}),
                        "query": query_text,
                        "filters_applied": filters
                    }
                )

        except ImportError:
            logger.info("Elasticsearch not available, falling back to database search")
        except Exception as es_error:
            logger.warning(f"Elasticsearch search failed: {es_error}, falling back to database")

        # Fallback to database search
        from app import db, Product, ProductImage, Seller, ProductReview

        # Get query parameters
        page = request.args.get('page', 1, type=int)
        per_page = request.args.get('per_page', 20, type=int)
        category = request.args.get('category')
        brand = request.args.get('brand')
        min_price = request.args.get('min_price', type=float)
        max_price = request.args.get('max_price', type=float)
        min_rating = request.args.get('min_rating', type=float)
        search = request.args.get('search', '').strip() or request.args.get('q', '').strip()
        sort_by = request.args.get('sort_by', 'created_at')
        sort_order = request.args.get('sort_order', 'desc')
        in_stock_only = request.args.get('in_stock_only', '').lower() == 'true'
        verified_sellers_only = request.args.get('verified_sellers_only', '').lower() == 'true'
        seller_name = request.args.get('seller_name')

        # Validate pagination
        page, per_page = validate_pagination(page, per_page, max_per_page=50)

        # Build base query
        query = Product.query.filter_by(is_active=True)

        # Apply filters
        if category:
            query = query.filter(Product.category.ilike(f'%{category}%'))

        if brand:
            query = query.filter(Product.brand.ilike(f'%{brand}%'))

        if min_price is not None:
            query = query.filter(Product.price >= min_price)

        if max_price is not None:
            query = query.filter(Product.price <= max_price)

        if in_stock_only:
            query = query.filter(Product.stock_quantity > 0)

        if search:
            search_filter = f'%{search}%'
            query = query.filter(
                db.or_(
                    Product.name.ilike(search_filter),
                    Product.description.ilike(search_filter),
                    Product.category.ilike(search_filter),
                    Product.brand.ilike(search_filter)
                )
            )

        # Seller filters
        if seller_name or verified_sellers_only:
            query = query.join(Seller, Product.seller_id == Seller.id)

            if seller_name:
                query = query.filter(Seller.business_name.ilike(f'%{seller_name}%'))

            if verified_sellers_only:
                query = query.filter(Seller.is_verified == True)

        # Rating filter (requires subquery)
        if min_rating is not None:
            rating_subquery = db.session.query(
                ProductReview.product_id,
                db.func.avg(ProductReview.rating).label('avg_rating')
            ).group_by(ProductReview.product_id).subquery()

            query = query.join(rating_subquery, Product.id == rating_subquery.c.product_id)
            query = query.filter(rating_subquery.c.avg_rating >= min_rating)

        # Apply sorting
        if sort_by == 'name':
            order_by = Product.name.asc() if sort_order == 'asc' else Product.name.desc()
        elif sort_by == 'price':
            order_by = Product.price.asc() if sort_order == 'asc' else Product.price.desc()
        elif sort_by == 'rating':
            # Sort by average rating (requires join)
            rating_subquery = db.session.query(
                ProductReview.product_id,
                db.func.avg(ProductReview.rating).label('avg_rating')
            ).group_by(ProductReview.product_id).subquery()

            query = query.outerjoin(rating_subquery, Product.id == rating_subquery.c.product_id)
            order_by = rating_subquery.c.avg_rating.desc() if sort_order == 'desc' else rating_subquery.c.avg_rating.asc()
        else:  # created_at or relevance (fallback to created_at)
            order_by = Product.created_at.asc() if sort_order == 'asc' else Product.created_at.desc()

        query = query.order_by(order_by)

        # Execute paginated query
        paginated_products = query.paginate(
            page=page,
            per_page=per_page,
            error_out=False
        )

        # Format products data
        products_data = []
        for product in paginated_products.items:
            # Get first image
            first_image = ProductImage.query.filter_by(product_id=product.id).first()

            # Get seller info
            seller = Seller.query.get(product.seller_id) if hasattr(product, 'seller_id') and product.seller_id else None

            # Get average rating
            avg_rating = db.session.query(
                db.func.avg(ProductReview.rating)
            ).filter_by(product_id=product.id).scalar()

            products_data.append({
                "id": product.id,
                "name": product.name,
                "description": product.description,
                "price": float(product.price),
                "category": product.category,
                "brand": getattr(product, 'brand', None),
                "stock_quantity": product.stock_quantity,
                "rating": round(float(avg_rating), 2) if avg_rating else None,
                "image_url": first_image.image_url if first_image else None,
                "seller": {
                    "id": seller.id if seller else None,
                    "name": seller.business_name if seller else None,
                    "is_verified": seller.is_verified if seller else False
                } if seller else None,
                "created_at": product.created_at.isoformat() if product.created_at else None
            })

        # Calculate execution time
        execution_time = time.time() - start_time

        return paginated_response(
            data=products_data,
            page=page,
            per_page=per_page,
            total=paginated_products.total,
            message="Products retrieved successfully",
            meta={
                "search_engine": "database",
                "execution_time": round(execution_time, 3),
                "filters_applied": {
                    "category": category,
                    "brand": brand,
                    "price_range": [min_price, max_price] if min_price or max_price else None,
                    "min_rating": min_rating,
                    "in_stock_only": in_stock_only,
                    "verified_sellers_only": verified_sellers_only,
                    "seller_name": seller_name,
                    "search": search
                }
            }
        )

    except Exception as e:
        logger.error(f"Get products error: {e}")
        return error_response(
            message="Failed to retrieve products",
            status_code=500,
            error_code="PRODUCTS_FETCH_FAILED"
        )

@products_bp.route('/<int:product_id>', methods=['GET'])
@rate_limit_v2(limit=60, window=60, per='ip')
def get_product(product_id):
    """
    Get single product details.
    
    GET /api/v1/products/{product_id}
    """
    try:
        from app import Product, ProductImage, ProductReview
        
        product = Product.query.filter_by(id=product_id, is_active=True).first()
        if not product:
            return not_found_response("Product", product_id)
        
        # Get product images
        images = ProductImage.query.filter_by(product_id=product_id)\
            .order_by(ProductImage.display_order.asc()).all()
        
        # Get recent reviews
        reviews = ProductReview.query.filter_by(product_id=product_id)\
            .order_by(ProductReview.created_at.desc()).limit(5).all()
        
        # Format product data
        product_data = {
            "id": product.id,
            "name": product.name,
            "description": product.description,
            "price": float(product.price),
            "category": product.category,
            "stock_quantity": product.stock_quantity,
            "sku": product.sku,
            "rating": getattr(product, 'rating', 0),
            "reviews_count": len(reviews),
            "images": [
                {
                    "id": img.id,
                    "url": img.image_url,
                    "alt_text": img.alt_text,
                    "is_primary": img.is_primary
                }
                for img in images
            ],
            "recent_reviews": [
                {
                    "id": review.id,
                    "rating": review.rating,
                    "comment": review.comment,
                    "user_name": f"{review.user.first_name} {review.user.last_name[0]}." if review.user else "Anonymous",
                    "created_at": review.created_at.isoformat() if review.created_at else None
                }
                for review in reviews
            ],
            "created_at": product.created_at.isoformat() if product.created_at else None,
            "updated_at": product.updated_at.isoformat() if product.updated_at else None
        }
        
        return success_response(
            data=product_data,
            message="Product retrieved successfully"
        )
        
    except Exception as e:
        logger.error(f"Get product error: {e}")
        return error_response(
            message="Failed to retrieve product",
            status_code=500,
            error_code="PRODUCT_FETCH_FAILED"
        )



@products_bp.route('/best-sellers', methods=['GET'])
@rate_limit_v2(limit=60, window=60, per='ip')
def get_best_sellers():
    """
    Get best selling products.
    
    GET /api/v1/products/best-sellers
    """
    try:
        limit = request.args.get('limit', 8, type=int)
        limit = min(limit, 50)  # Max 50 items
        
        from app import db, Product, Sales
        
        # Get best sellers based on sales data
        best_sellers_query = db.session.query(
            Product,
            db.func.sum(Sales.quantity).label('total_sold')
        ).join(
            Sales, Product.id == Sales.product_id
        ).filter(
            Product.is_active == True
        ).group_by(
            Product.id
        ).order_by(
            db.desc('total_sold')
        ).limit(limit)
        
        products_data = []
        for product, total_sold in best_sellers_query.all():
            product_data = {
                "id": product.id,
                "name": product.name,
                "price": float(product.price),
                "category": product.category,
                "image_url": product.images[0].image_url if product.images else None,
                "total_sold": int(total_sold),
                "rating": getattr(product, 'rating', 0)
            }
            products_data.append(product_data)
        
        return success_response(
            data={"products": products_data},
            message="Best sellers retrieved successfully"
        )
        
    except Exception as e:
        logger.error(f"Get best sellers error: {e}")
        return error_response(
            message="Failed to retrieve best sellers",
            status_code=500,
            error_code="BEST_SELLERS_FETCH_FAILED"
        )

@products_bp.route('/new-arrivals', methods=['GET'])
@rate_limit_v2(limit=60, window=60, per='ip')
def get_new_arrivals():
    """
    Get newest products.

    GET /api/v1/products/new-arrivals
    """
    try:
        limit = min(request.args.get('limit', 20, type=int), 50)

        from app import Product, ProductImage

        # Get newest products
        new_products = Product.query.filter_by(
            is_active=True
        ).order_by(
            Product.created_at.desc()
        ).limit(limit).all()

        products_data = []
        for product in new_products:
            first_image = ProductImage.query.filter_by(product_id=product.id).first()

            products_data.append({
                "id": product.id,
                "name": product.name,
                "description": product.description,
                "price": float(product.price),
                "category": product.category,
                "stock_quantity": product.stock_quantity,
                "image_url": first_image.image_url if first_image else None,
                "created_at": product.created_at.isoformat() if product.created_at else None
            })

        return success_response(
            data=products_data,
            message="New arrivals retrieved successfully",
            meta={"total_products": len(products_data)}
        )

    except Exception as e:
        logger.error(f"Get new arrivals error: {e}")
        return error_response(
            message="Failed to retrieve new arrivals",
            status_code=500,
            error_code="NEW_ARRIVALS_FETCH_FAILED"
        )

@products_bp.route('/batch', methods=['POST'])
@rate_limit_v2(limit=30, window=60, per='ip')
@validate_content_type()
def get_products_batch():
    """
    Get multiple products by IDs in batch.

    POST /api/v1/products/batch
    """
    try:
        # Validate JSON
        is_valid, data = validate_json()
        if not is_valid:
            return data

        # Validate required fields
        required_fields = ['product_ids']
        is_valid, error_resp = validate_required_fields(data, required_fields)
        if not is_valid:
            return error_resp

        product_ids = data['product_ids']

        if not isinstance(product_ids, list) or len(product_ids) == 0:
            return validation_error_response(
                errors={"product_ids": ["Must be a non-empty list of product IDs"]},
                message="Invalid product IDs"
            )

        if len(product_ids) > 50:
            return validation_error_response(
                errors={"product_ids": ["Maximum 50 products allowed per batch request"]},
                message="Too many products requested"
            )

        from app import Product, ProductImage

        # Get products
        products = Product.query.filter(
            Product.id.in_(product_ids),
            Product.is_active == True
        ).all()

        products_data = []
        for product in products:
            first_image = ProductImage.query.filter_by(product_id=product.id).first()

            products_data.append({
                "id": product.id,
                "name": product.name,
                "description": product.description,
                "price": float(product.price),
                "category": product.category,
                "stock_quantity": product.stock_quantity,
                "image_url": first_image.image_url if first_image else None,
                "created_at": product.created_at.isoformat() if product.created_at else None
            })

        # Track which IDs were not found
        found_ids = [p["id"] for p in products_data]
        not_found_ids = [pid for pid in product_ids if pid not in found_ids]

        return success_response(
            data=products_data,
            message="Products retrieved successfully",
            meta={
                "requested_count": len(product_ids),
                "found_count": len(products_data),
                "not_found_ids": not_found_ids
            }
        )

    except Exception as e:
        logger.error(f"Get products batch error: {e}")
        return error_response(
            message="Failed to retrieve products batch",
            status_code=500,
            error_code="PRODUCTS_BATCH_FETCH_FAILED"
        )

@products_bp.route('/categories', methods=['GET'])
@rate_limit_v2(limit=60, window=60, per='ip')
def get_product_categories():
    """
    Get all product categories with product counts.

    GET /api/v1/products/categories
    """
    try:
        from app import db, Product

        # Get categories with product counts
        categories = db.session.query(
            Product.category,
            db.func.count(Product.id).label('product_count')
        ).filter(
            Product.is_active == True,
            Product.category.isnot(None)
        ).group_by(
            Product.category
        ).order_by(
            Product.category.asc()
        ).all()

        categories_data = []
        for category, count in categories:
            categories_data.append({
                "name": category,
                "product_count": int(count),
                "slug": category.lower().replace(' ', '-').replace('&', 'and')
            })

        return success_response(
            data=categories_data,
            message="Categories retrieved successfully",
            meta={"total_categories": len(categories_data)}
        )

    except Exception as e:
        logger.error(f"Get categories error: {e}")
        return error_response(
            message="Failed to retrieve categories",
            status_code=500,
            error_code="CATEGORIES_FETCH_FAILED"
        )

@products_bp.route('/categories/<category_name>/products', methods=['GET'])
@rate_limit_v2(limit=60, window=60, per='ip')
def get_products_by_category(category_name):
    """
    Get products by category with pagination and filtering.

    GET /api/v1/products/categories/{category_name}/products
    """
    try:
        # Decode category name
        category_name = category_name.replace('-', ' ').replace('and', '&')

        # Get query parameters
        page = request.args.get('page', 1, type=int)
        per_page = request.args.get('per_page', 20, type=int)
        min_price = request.args.get('min_price', type=float)
        max_price = request.args.get('max_price', type=float)
        sort_by = request.args.get('sort_by', 'created_at')
        sort_order = request.args.get('sort_order', 'desc')

        # Validate pagination
        page, per_page = validate_pagination(page, per_page, max_per_page=50)

        from app import db, Product, ProductImage

        # Build query
        query = Product.query.filter(
            Product.category.ilike(f'%{category_name}%'),
            Product.is_active == True
        )

        # Apply price filters
        if min_price is not None:
            query = query.filter(Product.price >= min_price)

        if max_price is not None:
            query = query.filter(Product.price <= max_price)

        # Apply sorting
        if sort_by == 'name':
            order_by = Product.name.asc() if sort_order == 'asc' else Product.name.desc()
        elif sort_by == 'price':
            order_by = Product.price.asc() if sort_order == 'asc' else Product.price.desc()
        else:  # created_at
            order_by = Product.created_at.asc() if sort_order == 'asc' else Product.created_at.desc()

        query = query.order_by(order_by)

        # Execute paginated query
        paginated_products = query.paginate(
            page=page,
            per_page=per_page,
            error_out=False
        )

        # Format products data
        products_data = []
        for product in paginated_products.items:
            first_image = ProductImage.query.filter_by(product_id=product.id).first()

            products_data.append({
                "id": product.id,
                "name": product.name,
                "description": product.description,
                "price": float(product.price),
                "category": product.category,
                "stock_quantity": product.stock_quantity,
                "image_url": first_image.image_url if first_image else None,
                "created_at": product.created_at.isoformat() if product.created_at else None
            })

        return paginated_response(
            data=products_data,
            page=page,
            per_page=per_page,
            total=paginated_products.total,
            message=f"Products in category '{category_name}' retrieved successfully",
            meta={
                "category": category_name,
                "filters_applied": {
                    "price_range": [min_price, max_price] if min_price or max_price else None
                }
            }
        )

    except Exception as e:
        logger.error(f"Get products by category error: {e}")
        return error_response(
            message="Failed to retrieve products by category",
            status_code=500,
            error_code="CATEGORY_PRODUCTS_FETCH_FAILED"
        )

@products_bp.route('/<int:product_id>/reviews', methods=['GET'])
@rate_limit_v2(limit=60, window=60, per='ip')
def get_product_reviews(product_id):
    """
    Get reviews for a specific product with pagination.

    GET /api/v1/products/{product_id}/reviews
    """
    try:
        page = request.args.get('page', 1, type=int)
        per_page = request.args.get('per_page', 20, type=int)
        sort_by = request.args.get('sort_by', 'created_at')  # created_at, rating, helpful
        sort_order = request.args.get('sort_order', 'desc')

        # Validate pagination
        page, per_page = validate_pagination(page, per_page, max_per_page=50)

        from app import db, Product, ProductReview, User

        # Check if product exists
        product = Product.query.filter_by(id=product_id, is_active=True).first()
        if not product:
            return not_found_response("Product", product_id)

        # Build query
        query = ProductReview.query.filter_by(product_id=product_id)

        # Apply sorting
        if sort_by == 'rating':
            order_by = ProductReview.rating.asc() if sort_order == 'asc' else ProductReview.rating.desc()
        elif sort_by == 'helpful':
            order_by = ProductReview.helpful_count.asc() if sort_order == 'asc' else ProductReview.helpful_count.desc()
        else:  # created_at
            order_by = ProductReview.created_at.asc() if sort_order == 'asc' else ProductReview.created_at.desc()

        query = query.order_by(order_by)

        # Execute paginated query
        paginated_reviews = query.paginate(
            page=page,
            per_page=per_page,
            error_out=False
        )

        # Format reviews data
        reviews_data = []
        for review in paginated_reviews.items:
            reviewer = User.query.get(review.user_id)

            reviews_data.append({
                "id": review.id,
                "rating": review.rating,
                "title": getattr(review, 'title', None),
                "comment": review.comment,
                "reviewer": {
                    "name": f"{reviewer.first_name} {reviewer.last_name[0]}." if reviewer else "Anonymous",
                    "verified_purchase": getattr(review, 'verified_purchase', False)
                },
                "helpful_count": getattr(review, 'helpful_count', 0),
                "images": getattr(review, 'images', []),
                "created_at": review.created_at.isoformat() if review.created_at else None
            })

        # Get review statistics
        review_stats = db.session.query(
            db.func.avg(ProductReview.rating).label('average_rating'),
            db.func.count(ProductReview.id).label('total_reviews'),
            db.func.sum(db.case([(ProductReview.rating == 5, 1)], else_=0)).label('five_star'),
            db.func.sum(db.case([(ProductReview.rating == 4, 1)], else_=0)).label('four_star'),
            db.func.sum(db.case([(ProductReview.rating == 3, 1)], else_=0)).label('three_star'),
            db.func.sum(db.case([(ProductReview.rating == 2, 1)], else_=0)).label('two_star'),
            db.func.sum(db.case([(ProductReview.rating == 1, 1)], else_=0)).label('one_star')
        ).filter_by(product_id=product_id).first()

        return paginated_response(
            data=reviews_data,
            page=page,
            per_page=per_page,
            total=paginated_reviews.total,
            message="Product reviews retrieved successfully",
            meta={
                "product_id": product_id,
                "review_statistics": {
                    "average_rating": round(float(review_stats.average_rating), 2) if review_stats.average_rating else 0,
                    "total_reviews": int(review_stats.total_reviews or 0),
                    "rating_distribution": {
                        "5": int(review_stats.five_star or 0),
                        "4": int(review_stats.four_star or 0),
                        "3": int(review_stats.three_star or 0),
                        "2": int(review_stats.two_star or 0),
                        "1": int(review_stats.one_star or 0)
                    }
                }
            }
        )

    except Exception as e:
        logger.error(f"Get product reviews error: {e}")
        return error_response(
            message="Failed to retrieve product reviews",
            status_code=500,
            error_code="PRODUCT_REVIEWS_FETCH_FAILED"
        )

@products_bp.route('/<int:product_id>/reviews', methods=['POST'])
@jwt_required_v2()
@validate_content_type()
@rate_limit_v2(limit=5, window=3600, per='user')  # 5 reviews per hour
def add_product_review(user, product_id):
    """
    Add a review for a product.

    POST /api/v1/products/{product_id}/reviews
    """
    try:
        # Validate JSON
        is_valid, data = validate_json()
        if not is_valid:
            return data

        # Validate required fields
        required_fields = ['rating', 'comment']
        is_valid, error_resp = validate_required_fields(data, required_fields)
        if not is_valid:
            return error_resp

        rating = data['rating']
        comment = data['comment'].strip()
        title = data.get('title', '').strip()

        # Validate rating
        if not isinstance(rating, int) or rating < 1 or rating > 5:
            return validation_error_response(
                errors={"rating": ["Rating must be an integer between 1 and 5"]},
                message="Invalid rating"
            )

        # Validate comment
        if len(comment) < 10:
            return validation_error_response(
                errors={"comment": ["Comment must be at least 10 characters long"]},
                message="Comment too short"
            )

        if len(comment) > 1000:
            return validation_error_response(
                errors={"comment": ["Comment must be less than 1000 characters"]},
                message="Comment too long"
            )

        from app import db, Product, ProductReview, Order, OrderItem

        # Check if product exists
        product = Product.query.filter_by(id=product_id, is_active=True).first()
        if not product:
            return not_found_response("Product", product_id)

        # Check if user already reviewed this product
        existing_review = ProductReview.query.filter_by(
            product_id=product_id,
            user_id=user.id
        ).first()

        if existing_review:
            return error_response(
                message="You have already reviewed this product",
                status_code=409,
                error_code="REVIEW_ALREADY_EXISTS"
            )

        # Check if user purchased this product (optional verification)
        verified_purchase = False
        user_order = db.session.query(Order).join(
            OrderItem, Order.id == OrderItem.order_id
        ).filter(
            Order.user_id == user.id,
            OrderItem.product_id == product_id,
            Order.status.in_(['completed', 'delivered'])
        ).first()

        if user_order:
            verified_purchase = True

        # Create new review
        new_review = ProductReview(
            product_id=product_id,
            user_id=user.id,
            rating=rating,
            title=title or None,
            comment=comment,
            verified_purchase=verified_purchase,
            helpful_count=0,
            created_at=datetime.utcnow()
        )

        db.session.add(new_review)
        db.session.commit()

        review_data = {
            "id": new_review.id,
            "rating": new_review.rating,
            "title": new_review.title,
            "comment": new_review.comment,
            "reviewer": {
                "name": f"{user.first_name} {user.last_name[0]}.",
                "verified_purchase": verified_purchase
            },
            "helpful_count": 0,
            "created_at": new_review.created_at.isoformat()
        }

        return success_response(
            data=review_data,
            message="Review added successfully",
            status_code=201
        )

    except Exception as e:
        logger.error(f"Add product review error: {e}")
        db.session.rollback()
        return error_response(
            message="Failed to add product review",
            status_code=500,
            error_code="PRODUCT_REVIEW_ADD_FAILED"
        )

@products_bp.route('/reviews/<int:review_id>/helpful', methods=['POST'])
@jwt_required_v2()
@rate_limit_v2(limit=20, window=3600, per='user')  # 20 helpful votes per hour
def mark_review_helpful(user, review_id):
    """
    Mark a review as helpful.

    POST /api/v1/products/reviews/{review_id}/helpful
    """
    try:
        from app import db, ProductReview, ReviewHelpful

        # Check if review exists
        review = ProductReview.query.get(review_id)
        if not review:
            return not_found_response("Review", review_id)

        # Check if user already marked this review as helpful
        existing_helpful = ReviewHelpful.query.filter_by(
            review_id=review_id,
            user_id=user.id
        ).first()

        if existing_helpful:
            # Remove helpful vote (toggle)
            db.session.delete(existing_helpful)
            review.helpful_count = max(0, (review.helpful_count or 0) - 1)
            action = "removed"
        else:
            # Add helpful vote
            new_helpful = ReviewHelpful(
                review_id=review_id,
                user_id=user.id,
                created_at=datetime.utcnow()
            )
            db.session.add(new_helpful)
            review.helpful_count = (review.helpful_count or 0) + 1
            action = "added"

        db.session.commit()

        return success_response(
            data={
                "review_id": review_id,
                "action": action,
                "helpful_count": review.helpful_count,
                "user_found_helpful": action == "added"
            },
            message=f"Helpful vote {action} successfully"
        )

    except Exception as e:
        logger.error(f"Mark review helpful error: {e}")
        db.session.rollback()
        return error_response(
            message="Failed to process helpful vote",
            status_code=500,
            error_code="REVIEW_HELPFUL_FAILED"
        )

@products_bp.route('/<int:product_id>/images', methods=['GET'])
@rate_limit_v2(limit=60, window=60, per='ip')
def get_product_images(product_id):
    """
    Get all images for a product.

    GET /api/v1/products/{product_id}/images
    """
    try:
        from app import Product, ProductImage

        # Check if product exists
        product = Product.query.filter_by(id=product_id, is_active=True).first()
        if not product:
            return not_found_response("Product", product_id)

        # Get product images
        images = ProductImage.query.filter_by(
            product_id=product_id
        ).order_by(ProductImage.display_order.asc()).all()

        images_data = []
        for image in images:
            images_data.append({
                "id": image.id,
                "url": image.image_url,
                "alt_text": image.alt_text or product.name,
                "display_order": image.display_order or 0,
                "is_primary": getattr(image, 'is_primary', False),
                "created_at": image.created_at.isoformat() if image.created_at else None
            })

        return success_response(
            data=images_data,
            message="Product images retrieved successfully",
            meta={
                "product_id": product_id,
                "total_images": len(images_data)
            }
        )

    except Exception as e:
        logger.error(f"Get product images error: {e}")
        return error_response(
            message="Failed to retrieve product images",
            status_code=500,
            error_code="PRODUCT_IMAGES_FETCH_FAILED"
        )

@products_bp.route('/<int:product_id>/images', methods=['POST'])
@jwt_required_v2()
@validate_content_type()
@rate_limit_v2(limit=10, window=3600, per='user')  # 10 image uploads per hour
def add_product_image(user, product_id):
    """
    Add an image to a product (seller only).

    POST /api/v1/products/{product_id}/images
    """
    try:
        # Validate JSON
        is_valid, data = validate_json()
        if not is_valid:
            return data

        # Validate required fields
        required_fields = ['image_url']
        is_valid, error_resp = validate_required_fields(data, required_fields)
        if not is_valid:
            return error_resp

        image_url = data['image_url'].strip()
        alt_text = data.get('alt_text', '').strip()
        display_order = data.get('display_order', 0)
        is_primary = data.get('is_primary', False)

        from app import db, Product, ProductImage, Seller

        # Check if product exists
        product = Product.query.filter_by(id=product_id, is_active=True).first()
        if not product:
            return not_found_response("Product", product_id)

        # Check if user is the seller of this product
        if hasattr(product, 'seller_id') and product.seller_id:
            seller = Seller.query.filter_by(user_id=user.id).first()
            if not seller or seller.id != product.seller_id:
                return error_response(
                    message="You can only add images to your own products",
                    status_code=403,
                    error_code="UNAUTHORIZED_PRODUCT_ACCESS"
                )

        # Validate image URL
        if not image_url.startswith(('http://', 'https://')):
            return validation_error_response(
                errors={"image_url": ["Must be a valid HTTP/HTTPS URL"]},
                message="Invalid image URL"
            )

        # Check image limit (max 10 images per product)
        existing_images_count = ProductImage.query.filter_by(product_id=product_id).count()
        if existing_images_count >= 10:
            return error_response(
                message="Maximum 10 images allowed per product",
                status_code=400,
                error_code="IMAGE_LIMIT_EXCEEDED"
            )

        # If this is set as primary, remove primary flag from other images
        if is_primary:
            ProductImage.query.filter_by(product_id=product_id).update(
                {'is_primary': False}
            )

        # Create new image
        new_image = ProductImage(
            product_id=product_id,
            image_url=image_url,
            alt_text=alt_text or None,
            display_order=display_order,
            is_primary=is_primary,
            created_at=datetime.utcnow()
        )

        db.session.add(new_image)
        db.session.commit()

        image_data = {
            "id": new_image.id,
            "url": new_image.image_url,
            "alt_text": new_image.alt_text,
            "display_order": new_image.display_order,
            "is_primary": new_image.is_primary,
            "created_at": new_image.created_at.isoformat()
        }

        return success_response(
            data=image_data,
            message="Product image added successfully",
            status_code=201
        )

    except Exception as e:
        logger.error(f"Add product image error: {e}")
        db.session.rollback()
        return error_response(
            message="Failed to add product image",
            status_code=500,
            error_code="PRODUCT_IMAGE_ADD_FAILED"
        )

@products_bp.route('/<int:product_id>/variants', methods=['GET'])
@rate_limit_v2(limit=60, window=60, per='ip')
def get_product_variants(product_id):
    """
    Get all variants for a product.

    GET /api/v1/products/{product_id}/variants
    """
    try:
        from app import Product, ProductVariant

        # Check if product exists
        product = Product.query.filter_by(id=product_id, is_active=True).first()
        if not product:
            return not_found_response("Product", product_id)

        # Get product variants
        variants = ProductVariant.query.filter_by(
            product_id=product_id,
            is_active=True
        ).order_by(ProductVariant.created_at.asc()).all()

        variants_data = []
        for variant in variants:
            variants_data.append({
                "id": variant.id,
                "name": variant.name,
                "sku": variant.sku,
                "price": float(variant.price) if variant.price else float(product.price),
                "stock_quantity": variant.stock_quantity,
                "attributes": variant.attributes or {},  # JSON field with size, color, etc.
                "image_url": variant.image_url,
                "is_default": getattr(variant, 'is_default', False),
                "created_at": variant.created_at.isoformat() if variant.created_at else None
            })

        return success_response(
            data=variants_data,
            message="Product variants retrieved successfully",
            meta={
                "product_id": product_id,
                "total_variants": len(variants_data)
            }
        )

    except Exception as e:
        logger.error(f"Get product variants error: {e}")
        return error_response(
            message="Failed to retrieve product variants",
            status_code=500,
            error_code="PRODUCT_VARIANTS_FETCH_FAILED"
        )

@products_bp.route('/<int:product_id>/variants', methods=['POST'])
@jwt_required_v2()
@validate_content_type()
@rate_limit_v2(limit=10, window=3600, per='user')  # 10 variant creations per hour
def add_product_variant(user, product_id):
    """
    Add a variant to a product (seller only).

    POST /api/v1/products/{product_id}/variants
    """
    try:
        # Validate JSON
        is_valid, data = validate_json()
        if not is_valid:
            return data

        # Validate required fields
        required_fields = ['name', 'stock_quantity']
        is_valid, error_resp = validate_required_fields(data, required_fields)
        if not is_valid:
            return error_resp

        name = data['name'].strip()
        sku = data.get('sku', '').strip()
        price = data.get('price')
        stock_quantity = data['stock_quantity']
        attributes = data.get('attributes', {})
        image_url = data.get('image_url', '').strip()
        is_default = data.get('is_default', False)

        from app import db, Product, ProductVariant, Seller

        # Check if product exists
        product = Product.query.filter_by(id=product_id, is_active=True).first()
        if not product:
            return not_found_response("Product", product_id)

        # Check if user is the seller of this product
        if hasattr(product, 'seller_id') and product.seller_id:
            seller = Seller.query.filter_by(user_id=user.id).first()
            if not seller or seller.id != product.seller_id:
                return error_response(
                    message="You can only add variants to your own products",
                    status_code=403,
                    error_code="UNAUTHORIZED_PRODUCT_ACCESS"
                )

        # Validate stock quantity
        if not isinstance(stock_quantity, int) or stock_quantity < 0:
            return validation_error_response(
                errors={"stock_quantity": ["Stock quantity must be a non-negative integer"]},
                message="Invalid stock quantity"
            )

        # Validate price if provided
        if price is not None:
            is_valid_price, normalized_price = validate_price(price)
            if not is_valid_price:
                return validation_error_response(
                    errors={"price": ["Invalid price format"]},
                    message="Invalid price"
                )
        else:
            normalized_price = None

        # Check if SKU already exists (if provided)
        if sku:
            existing_variant = ProductVariant.query.filter_by(sku=sku).first()
            if existing_variant:
                return error_response(
                    message="Variant with this SKU already exists",
                    status_code=409,
                    error_code="SKU_EXISTS"
                )

        # If this is set as default, remove default flag from other variants
        if is_default:
            ProductVariant.query.filter_by(product_id=product_id).update(
                {'is_default': False}
            )

        # Create new variant
        new_variant = ProductVariant(
            product_id=product_id,
            name=name,
            sku=sku or None,
            price=normalized_price,
            stock_quantity=stock_quantity,
            attributes=attributes,
            image_url=image_url or None,
            is_default=is_default,
            is_active=True,
            created_at=datetime.utcnow()
        )

        db.session.add(new_variant)
        db.session.commit()

        variant_data = {
            "id": new_variant.id,
            "name": new_variant.name,
            "sku": new_variant.sku,
            "price": float(new_variant.price) if new_variant.price else float(product.price),
            "stock_quantity": new_variant.stock_quantity,
            "attributes": new_variant.attributes,
            "image_url": new_variant.image_url,
            "is_default": new_variant.is_default,
            "created_at": new_variant.created_at.isoformat()
        }

        return success_response(
            data=variant_data,
            message="Product variant added successfully",
            status_code=201
        )

    except Exception as e:
        logger.error(f"Add product variant error: {e}")
        db.session.rollback()
        return error_response(
            message="Failed to add product variant",
            status_code=500,
            error_code="PRODUCT_VARIANT_ADD_FAILED"
        )

@products_bp.route('/availability-notifications', methods=['GET'])
@jwt_required_v2()
@rate_limit_v2(limit=60, window=60, per='user')
def get_availability_notifications(user):
    """
    Get user's product availability notifications.

    GET /api/v1/products/availability-notifications
    """
    try:
        page = request.args.get('page', 1, type=int)
        per_page = request.args.get('per_page', 20, type=int)

        # Validate pagination
        page, per_page = validate_pagination(page, per_page, max_per_page=50)

        from app import db, AvailabilityNotification, Product, ProductImage

        # Get user's notifications
        query = AvailabilityNotification.query.filter_by(
            user_id=user.id,
            is_active=True
        ).order_by(AvailabilityNotification.created_at.desc())

        # Execute paginated query
        paginated_notifications = query.paginate(
            page=page,
            per_page=per_page,
            error_out=False
        )

        notifications_data = []
        for notification in paginated_notifications.items:
            product = Product.query.get(notification.product_id)
            if product:
                first_image = ProductImage.query.filter_by(product_id=product.id).first()

                notifications_data.append({
                    "id": notification.id,
                    "product": {
                        "id": product.id,
                        "name": product.name,
                        "price": float(product.price),
                        "stock_quantity": product.stock_quantity,
                        "image_url": first_image.image_url if first_image else None
                    },
                    "notification_sent": getattr(notification, 'notification_sent', False),
                    "created_at": notification.created_at.isoformat() if notification.created_at else None
                })

        return paginated_response(
            data=notifications_data,
            page=page,
            per_page=per_page,
            total=paginated_notifications.total,
            message="Availability notifications retrieved successfully"
        )

    except Exception as e:
        logger.error(f"Get availability notifications error: {e}")
        return error_response(
            message="Failed to retrieve availability notifications",
            status_code=500,
            error_code="AVAILABILITY_NOTIFICATIONS_FETCH_FAILED"
        )

@products_bp.route('/availability-notifications', methods=['POST'])
@jwt_required_v2()
@validate_content_type()
@rate_limit_v2(limit=20, window=3600, per='user')  # 20 notifications per hour
def create_availability_notification(user):
    """
    Create a notification for when a product becomes available.

    POST /api/v1/products/availability-notifications
    """
    try:
        # Validate JSON
        is_valid, data = validate_json()
        if not is_valid:
            return data

        # Validate required fields
        required_fields = ['product_id']
        is_valid, error_resp = validate_required_fields(data, required_fields)
        if not is_valid:
            return error_resp

        product_id = data['product_id']

        from app import db, Product, AvailabilityNotification

        # Check if product exists
        product = Product.query.filter_by(id=product_id, is_active=True).first()
        if not product:
            return not_found_response("Product", product_id)

        # Check if product is already in stock
        if product.stock_quantity > 0:
            return error_response(
                message="Product is currently in stock",
                status_code=400,
                error_code="PRODUCT_IN_STOCK"
            )

        # Check if user already has a notification for this product
        existing_notification = AvailabilityNotification.query.filter_by(
            user_id=user.id,
            product_id=product_id,
            is_active=True
        ).first()

        if existing_notification:
            return error_response(
                message="You already have a notification for this product",
                status_code=409,
                error_code="NOTIFICATION_EXISTS"
            )

        # Create new notification
        new_notification = AvailabilityNotification(
            user_id=user.id,
            product_id=product_id,
            is_active=True,
            notification_sent=False,
            created_at=datetime.utcnow()
        )

        db.session.add(new_notification)
        db.session.commit()

        notification_data = {
            "id": new_notification.id,
            "product_id": product_id,
            "product_name": product.name,
            "created_at": new_notification.created_at.isoformat()
        }

        return success_response(
            data=notification_data,
            message="Availability notification created successfully",
            status_code=201
        )

    except Exception as e:
        logger.error(f"Create availability notification error: {e}")
        db.session.rollback()
        return error_response(
            message="Failed to create availability notification",
            status_code=500,
            error_code="AVAILABILITY_NOTIFICATION_CREATE_FAILED"
        )

@products_bp.route('/comparison', methods=['GET'])
@rate_limit_v2(limit=30, window=60, per='ip')
def get_product_comparison():
    """
    Compare multiple products side by side.

    GET /api/v1/products/comparison?product_ids=1,2,3
    """
    try:
        product_ids_param = request.args.get('product_ids', '')

        if not product_ids_param:
            return validation_error_response(
                errors={"product_ids": ["Product IDs parameter is required"]},
                message="Missing product IDs"
            )

        try:
            product_ids = [int(pid.strip()) for pid in product_ids_param.split(',') if pid.strip()]
        except ValueError:
            return validation_error_response(
                errors={"product_ids": ["Invalid product ID format"]},
                message="Invalid product IDs"
            )

        if len(product_ids) < 2:
            return validation_error_response(
                errors={"product_ids": ["At least 2 products required for comparison"]},
                message="Insufficient products for comparison"
            )

        if len(product_ids) > 5:
            return validation_error_response(
                errors={"product_ids": ["Maximum 5 products allowed for comparison"]},
                message="Too many products for comparison"
            )

        from app import db, Product, ProductImage, ProductReview, Seller

        # Get products
        products = Product.query.filter(
            Product.id.in_(product_ids),
            Product.is_active == True
        ).all()

        if len(products) != len(product_ids):
            found_ids = [p.id for p in products]
            missing_ids = [pid for pid in product_ids if pid not in found_ids]
            return error_response(
                message=f"Products not found: {missing_ids}",
                status_code=404,
                error_code="PRODUCTS_NOT_FOUND"
            )

        # Format comparison data
        comparison_data = []
        for product in products:
            # Get first image
            first_image = ProductImage.query.filter_by(product_id=product.id).first()

            # Get seller info
            seller = Seller.query.get(product.seller_id) if hasattr(product, 'seller_id') and product.seller_id else None

            # Get average rating
            avg_rating = db.session.query(
                db.func.avg(ProductReview.rating)
            ).filter_by(product_id=product.id).scalar()

            # Get review count
            review_count = ProductReview.query.filter_by(product_id=product.id).count()

            comparison_data.append({
                "id": product.id,
                "name": product.name,
                "description": product.description,
                "price": float(product.price),
                "category": product.category,
                "brand": getattr(product, 'brand', None),
                "stock_quantity": product.stock_quantity,
                "rating": round(float(avg_rating), 2) if avg_rating else 0,
                "review_count": review_count,
                "sustainability_score": getattr(product, 'sustainability_score', None),
                "image_url": first_image.image_url if first_image else None,
                "seller": {
                    "name": seller.business_name if seller else None,
                    "is_verified": seller.is_verified if seller else False
                } if seller else None,
                "specifications": getattr(product, 'specifications', {}),
                "created_at": product.created_at.isoformat() if product.created_at else None
            })

        return success_response(
            data=comparison_data,
            message="Product comparison retrieved successfully",
            meta={
                "compared_products": len(comparison_data),
                "comparison_id": f"comp_{'-'.join(map(str, product_ids))}"
            }
        )

    except Exception as e:
        logger.error(f"Get product comparison error: {e}")
        return error_response(
            message="Failed to retrieve product comparison",
            status_code=500,
            error_code="PRODUCT_COMPARISON_FAILED"
        )
