#!/usr/bin/env python3
"""
Hybrid Server Runner for Allora E-commerce Platform
==================================================

Automatically selects the best available server:
1. Gunicorn + Eventlet (Production + SocketIO)
2. <PERSON>lask-SocketIO (Current fallback)
3. Waitress (HTTP-only fallback)

Author: Allora Development Team
"""

import sys
import os
import time
from datetime import datetime

# Add current directory to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Configuration
DEBUG_ROUTES = os.getenv('DEBUG_ROUTES', 'false').lower() == 'true'
HOST = os.getenv('HOST', '127.0.0.1')
PORT = int(os.getenv('PORT', 5000))
THREADS = int(os.getenv('THREADS', 6))

def print_startup_banner():
    """Print startup banner with system info"""
    print("=" * 60)
    print("🚀 ALLORA HYBRID SERVER STARTING")
    print("=" * 60)
    print(f"📅 Startup Time: {datetime.now().isoformat()}")
    print(f"🌐 Host: {HOST}:{PORT}")
    print(f"🐍 Python: {sys.version.split()[0]}")
    print(f"📁 Working Directory: {os.getcwd()}")
    print("=" * 60)

def check_dependencies():
    """Check which server dependencies are available"""
    dependencies = {
        'gunicorn': False,
        'eventlet': False,
        'gevent': False,
        'socketio': False,
        'waitress': True  # Always available
    }
    
    try:
        import gunicorn
        dependencies['gunicorn'] = True
    except ImportError:
        pass
    
    try:
        import eventlet
        dependencies['eventlet'] = True
    except ImportError:
        pass
    
    try:
        import gevent
        dependencies['gevent'] = True
    except ImportError:
        pass
    
    try:
        from app import socketio
        dependencies['socketio'] = socketio is not None
    except ImportError:
        pass
    
    return dependencies

def run_gunicorn_eventlet(app):
    """Run with Gunicorn + Eventlet (Best performance + SocketIO)"""
    try:
        print("🚀 Starting with Gunicorn + Eventlet")
        print("⚡ Performance: 10,000+ concurrent connections")
        print("🔌 WebSocket: Full SocketIO support")
        print("✅ Production-ready server")
        print("-" * 40)
        
        from gunicorn.app.base import BaseApplication
        
        class StandaloneApplication(BaseApplication):
            def __init__(self, app, options=None):
                self.options = options or {}
                self.application = app
                super().__init__()
            
            def load_config(self):
                config = {
                    'bind': f'{HOST}:{PORT}',
                    'workers': 1,
                    'worker_class': 'eventlet',
                    'worker_connections': 1000,
                    'timeout': 30,
                    'keepalive': 2,
                    'max_requests': 1000,
                    'max_requests_jitter': 100,
                    'preload_app': True,
                    'access_log': '-',
                    'error_log': '-',
                    'log_level': 'info'
                }
                
                for key, value in config.items():
                    if key in self.cfg.settings:
                        self.cfg.set(key.lower(), value)
        
            def load(self):
                return self.application
        
        StandaloneApplication(app).run()
        return True
        
    except Exception as e:
        print(f"❌ Gunicorn+Eventlet failed: {e}")
        return False

def run_socketio_server(app):
    """Run with Flask-SocketIO server (Good performance + SocketIO)"""
    try:
        from app import socketio
        if not socketio:
            return False
            
        print("🔌 Starting with Flask-SocketIO server")
        print("⚡ Performance: 1,000+ concurrent connections")
        print("🔌 WebSocket: Full SocketIO support")
        print("⚠️  Note: Development server (consider upgrading to Gunicorn)")
        print("-" * 40)
        
        socketio.run(
            app, 
            host=HOST, 
            port=PORT, 
            debug=False,
            allow_unsafe_werkzeug=True
        )
        return True
        
    except Exception as e:
        print(f"❌ SocketIO server failed: {e}")
        return False

def run_waitress_server(app):
    """Run with Waitress (Basic performance, no SocketIO)"""
    try:
        from waitress import serve
        
        print("⚠️  Starting with Waitress (HTTP-only)")
        print("⚡ Performance: 500+ concurrent connections")
        print("❌ WebSocket: SocketIO features DISABLED")
        print("💡 Install 'pip install gunicorn eventlet' for better performance")
        print("-" * 40)
        
        serve(app, host=HOST, port=PORT, threads=THREADS)
        return True
        
    except Exception as e:
        print(f"❌ Waitress server failed: {e}")
        return False

def initialize_app():
    """Initialize the Flask application and database"""
    try:
        from app import app, db
        
        print("🔧 Initializing application...")
        
        with app.app_context():
            # Initialize database and components
            from app import (
                initialize_payment_gateways, 
                initialize_admin_user, 
                initialize_oauth_providers
            )
            
            try:
                db.create_all()
                print("✅ Database tables created/verified")
            except Exception as e:
                print(f"⚠️  Database initialization warning: {e}")
            
            try:
                initialize_payment_gateways()
                print("✅ Payment gateways initialized")
            except Exception as e:
                print(f"⚠️  Payment gateways warning: {e}")
            
            try:
                initialize_admin_user()
                print("✅ Admin user initialized")
            except Exception as e:
                print(f"⚠️  Admin user warning: {e}")
            
            try:
                initialize_oauth_providers()
                print("✅ OAuth providers initialized")
            except Exception as e:
                print(f"⚠️  OAuth providers warning: {e}")
        
        return app
        
    except Exception as e:
        print(f"❌ Application initialization failed: {e}")
        return None

def main():
    """Main server selection and startup logic"""
    print_startup_banner()
    
    # Check available dependencies
    deps = check_dependencies()
    print("🔍 Checking available server options...")
    print(f"   Gunicorn: {'✅' if deps['gunicorn'] else '❌'}")
    print(f"   Eventlet: {'✅' if deps['eventlet'] else '❌'}")
    print(f"   Gevent: {'✅' if deps['gevent'] else '❌'}")
    print(f"   SocketIO: {'✅' if deps['socketio'] else '❌'}")
    print(f"   Waitress: {'✅' if deps['waitress'] else '❌'}")
    print()
    
    # Initialize application
    app = initialize_app()
    if not app:
        print("❌ Failed to initialize application")
        sys.exit(1)
    
    # Print route information
    route_count = len(list(app.url_map.iter_rules()))
    print(f"📍 Total API routes registered: {route_count}")
    
    if DEBUG_ROUTES:
        print("\n=== REGISTERED ROUTES ===")
        for rule in app.url_map.iter_rules():
            print(f"{rule.rule} -> {rule.endpoint} [{', '.join(rule.methods)}]")
    
    print(f"🌐 Server will be available at: http://{HOST}:{PORT}")
    print(f"📊 Health check: http://{HOST}:{PORT}/api/health")
    print("=" * 60)
    
    # Server selection logic
    print("🎯 Selecting optimal server...")
    
    # Option 1: Try Gunicorn + Eventlet (Best)
    if deps['gunicorn'] and deps['eventlet']:
        if run_gunicorn_eventlet(app):
            return
    else:
        missing = []
        if not deps['gunicorn']:
            missing.append('gunicorn')
        if not deps['eventlet']:
            missing.append('eventlet')
        print(f"⚠️  Gunicorn+Eventlet unavailable (missing: {', '.join(missing)})")
        print("💡 Install with: pip install gunicorn eventlet")
    
    # Option 2: Try SocketIO server (Good)
    if deps['socketio']:
        if run_socketio_server(app):
            return
    else:
        print("⚠️  SocketIO server unavailable")
    
    # Option 3: Fallback to Waitress (Basic)
    if deps['waitress']:
        if run_waitress_server(app):
            return
    
    # If all options failed
    print("❌ All server options failed!")
    print("💡 Check your installation and try again")
    sys.exit(1)

if __name__ == '__main__':
    try:
        main()
    except KeyboardInterrupt:
        print("\n🛑 Server stopped by user")
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)